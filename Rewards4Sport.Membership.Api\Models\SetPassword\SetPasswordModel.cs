﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;

namespace Rewards4Sport.Membership.Api.Models.SetPassword
{
    public class SetPasswordModel
    {
        public int MemberId { get; set; }
        public Guid UserId { get; set; }
        public string ApplicationName { get; set; }
        public string RemoteIpAddress { get; set; }
        public string NewPassword { get; set; }

        public string SecurityKey { get; set; }
    }

    public class SetPasswordModelModelValidator : AbstractValidator<SetPasswordModel>
    {
        public SetPasswordModelModelValidator()
        {
            RuleFor(password => password.MemberId)
                .GreaterThan(0);

            RuleFor(password => password.RemoteIpAddress)
                .NotNull();

            RuleFor(password => password.UserId)
                .NotEqual(Guid.Empty);

            RuleFor(password => password.ApplicationName)
                .NotNull()
                .MaximumLength(50)
                .Must(application => Constants.ApplicationList.Contains(application));

            RuleFor(member => member.NewPassword)
                .NotNull()
                .Matches(Constants.PasswordComplexity)
                .MaximumLength(50);

            RuleFor(password => password.SecurityKey)
                .Equals(ConfigurationManager.AppSettings["SecretKey"]);

        }
    }
}
