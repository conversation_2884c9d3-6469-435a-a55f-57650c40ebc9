﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using BO2016;
using Rewards4Sport.Membership.Api.Data;

namespace Rewards4Sport.Membership.Api.Helpers
{
    public class QuidcoHelper
    {
        const string cachkey = "quidcokeys";

        public static ApplicationSkins GetQuidcoAccount(string application)
        {
            var domainName = application + ".com";

            if (HttpContext.Current.Application[cachkey] != null)
                return GetFromCache(application, domainName);

            using (var legacyCms = new CMSEntities())
            {
                var quidcoKeys = legacyCms.ApplicationSkins.ToList();
                HttpContext.Current.Application[cachkey] = quidcoKeys;
            }
            return GetFromCache(application, domainName);
        }

        private static ApplicationSkins GetFromCache(string application, string domainName)
        {
            var quidcoKeys = HttpContext.Current.Application[cachkey] as List<ApplicationSkin>;

            var key =
                quidcoKeys.Single(m =>
                    m.ApplicationName.ToLower() == application.ToLower() &&
                    m.skinDomain.ToLower() == domainName.ToLower());

            return new ApplicationSkins()
            {
                sQuidcoID = key.skinQuidcoID,
                sQuidcoKey = key.skinQuidcoKey
            };
        }
    }
}