﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="MemberProfileModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="Teams">
          <Key>
            <PropertyRef Name="teamID" />
          </Key>
          <Property Name="teamID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="teamName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="applicationName" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="teamRedemptionPartner" Type="bit" Nullable="false" />
          <Property Name="teamDateAdded" Type="datetime" Nullable="false" />
          <Property Name="teamCustomSignupURL" Type="varchar" MaxLength="50" />
          <Property Name="teamCustomSignupPgae" Type="bit" Nullable="false" />
          <Property Name="teamSuppress" Type="bit" Nullable="false" />
          <Property Name="teamNicknames" Type="varchar" MaxLength="255" />
          <Property Name="teamInternational" Type="bit" />
        </EntityType>
        <EntityType Name="TeamSupporters">
          <Key>
            <PropertyRef Name="supporterID" />
          </Key>
          <Property Name="supporterID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="memberID" Type="int" Nullable="false" />
          <Property Name="teamID" Type="int" Nullable="false" />
          <Property Name="supportedDate" Type="datetime" Nullable="false" />
          <Property Name="supportedSuppress" Type="bit" Nullable="false" />
        </EntityType>
        <EntityContainer Name="MemberProfileModelStoreContainer">
          <EntitySet Name="Teams" EntityType="Self.Teams" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TeamSupporters" EntityType="Self.TeamSupporters" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="MemberProfileModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="Team">
          <Key>
            <PropertyRef Name="teamID" />
          </Key>
          <Property Name="teamID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="teamName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="applicationName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="teamRedemptionPartner" Type="Boolean" Nullable="false" />
          <Property Name="teamDateAdded" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="teamCustomSignupURL" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="teamCustomSignupPgae" Type="Boolean" Nullable="false" />
          <Property Name="teamSuppress" Type="Boolean" Nullable="false" />
          <Property Name="teamNicknames" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="teamInternational" Type="Boolean" />
        </EntityType>
        <EntityType Name="TeamSupporter">
          <Key>
            <PropertyRef Name="supporterID" />
          </Key>
          <Property Name="supporterID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="memberID" Type="Int32" Nullable="false" />
          <Property Name="teamID" Type="Int32" Nullable="false" />
          <Property Name="supportedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="supportedSuppress" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityContainer Name="MemberProfileEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="Teams" EntityType="Self.Team" />
          <EntitySet Name="TeamSupporters" EntityType="Self.TeamSupporter" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="MemberProfileModelStoreContainer" CdmEntityContainer="MemberProfileEntities">
          <EntitySetMapping Name="Teams">
            <EntityTypeMapping TypeName="MemberProfileModel.Team">
              <MappingFragment StoreEntitySet="Teams">
                <ScalarProperty Name="teamID" ColumnName="teamID" />
                <ScalarProperty Name="teamName" ColumnName="teamName" />
                <ScalarProperty Name="applicationName" ColumnName="applicationName" />
                <ScalarProperty Name="teamRedemptionPartner" ColumnName="teamRedemptionPartner" />
                <ScalarProperty Name="teamDateAdded" ColumnName="teamDateAdded" />
                <ScalarProperty Name="teamCustomSignupURL" ColumnName="teamCustomSignupURL" />
                <ScalarProperty Name="teamCustomSignupPgae" ColumnName="teamCustomSignupPgae" />
                <ScalarProperty Name="teamSuppress" ColumnName="teamSuppress" />
                <ScalarProperty Name="teamNicknames" ColumnName="teamNicknames" />
                <ScalarProperty Name="teamInternational" ColumnName="teamInternational" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TeamSupporters">
            <EntityTypeMapping TypeName="MemberProfileModel.TeamSupporter">
              <MappingFragment StoreEntitySet="TeamSupporters">
                <ScalarProperty Name="supporterID" ColumnName="supporterID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="teamID" ColumnName="teamID" />
                <ScalarProperty Name="supportedDate" ColumnName="supportedDate" />
                <ScalarProperty Name="supportedSuppress" ColumnName="supportedSuppress" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>