﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7C87C35C-1D18-469C-A6BC-52937125D97A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Rewards4Sport.Membership.Api</RootNamespace>
    <AssemblyName>Rewards4Sport.Membership.Api</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44316</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BO2016">
      <HintPath>..\Libraries\BO2016.dll</HintPath>
    </Reference>
    <Reference Include="CommonServiceLocator, Version=2.0.7.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.2.0.7\lib\net48\CommonServiceLocator.dll</HintPath>
    </Reference>
    <Reference Include="DAL2016">
      <HintPath>..\Libraries\DAL2016.dll</HintPath>
    </Reference>
    <Reference Include="ENC2016, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\ENC2016.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Exceptionless, Version=6.0.0.0, Culture=neutral, PublicKeyToken=fc181f0a46f65747, processorArchitecture=MSIL">
      <HintPath>..\packages\Exceptionless.6.0.3\lib\net462\Exceptionless.dll</HintPath>
    </Reference>
    <Reference Include="Exceptionless.Mvc, Version=6.0.0.0, Culture=neutral, PublicKeyToken=fc181f0a46f65747, processorArchitecture=MSIL">
      <HintPath>..\packages\Exceptionless.Mvc.6.0.3\lib\net462\Exceptionless.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation, Version=1*******, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.11.9.0\lib\netstandard2.0\FluentValidation.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Core, Version=1.8.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Core.1.8.7\lib\net46\Hangfire.Core.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Pro.Redis, Version=3.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Pro.Redis.3.0.4\lib\net46\Hangfire.Pro.Redis.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.SqlServer, Version=1.8.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.SqlServer.1.8.7\lib\net451\Hangfire.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.StructureMap, Version=1.5.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.StructureMap3.1.5.3\lib\net45\Hangfire.StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=4.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.2.2\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.8\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="QUIDCO2016">
      <HintPath>..\Libraries\QUIDCO2016.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=110.2.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.110.2.0\lib\net471\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StructureMap.4.7.1\lib\net45\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap.Web, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\structuremap.web.4.0.0.315\lib\net40\StructureMap.Web.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.8.0.0\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.Metadata.8.0.0\lib\net462\System.Reflection.Metadata.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.0\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="UTL2016">
      <HintPath>..\Libraries\UTL2016.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=*******, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="WebsiteFunctions">
      <HintPath>..\Libraries\WebsiteFunctions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\StructuremapMvc.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\MemberController.cs" />
    <Compile Include="Data\Affiliate.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ApplicationSkin.cs">
      <DependentUpon>LegacyCmsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\AspNetMembershipModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AspNetMembershipModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\AspNetMembershipModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\AspNetMembershipModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AspNetMembershipModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Applications.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Membership.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Membership_GetPasswordWithFormat_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Membership_GetPassword_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Membership_GetUserByName_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Membership_GetUserByUserId_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_SchemaVersions.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Usernames.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\aspnet_Users.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ClubApplication.cs">
      <DependentUpon>ClubsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ClubMember.cs">
      <DependentUpon>ClubsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ClubsModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClubsModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ClubsModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClubsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ClubsModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClubsModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\GetAffiliateMemberSources_ByAffiliateIDMemberID_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\GetAffiliateMemberSources_ByAffiliateIDValue_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\GetAffiliateMemberSources_ByMemberID_Result.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\LegacyCmsModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LegacyCmsModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\LegacyCmsModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LegacyCmsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\LegacyCmsModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LegacyCmsModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\LoggingModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LoggingModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\LoggingModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LoggingModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\LoggingModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LoggingModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\Member.cs">
      <DependentUpon>AspNetMembershipModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\MemberProfileModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MemberProfileModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\MemberProfileModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MemberProfileModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\MemberProfileModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MemberProfileModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\PartnerClub.cs">
      <DependentUpon>ClubsModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\Rewards4MembershipProvider.cs" />
    <Compile Include="Data\Team.cs">
      <DependentUpon>MemberProfileModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\TeamSupporter.cs">
      <DependentUpon>MemberProfileModel.tt</DependentUpon>
    </Compile>
    <Compile Include="DependencyResolution\ControllerConvention.cs" />
    <Compile Include="DependencyResolution\DefaultRegistry.cs" />
    <Compile Include="DependencyResolution\IoC.cs" />
    <Compile Include="DependencyResolution\StructureMapDependencyScope.cs" />
    <Compile Include="DependencyResolution\StructureMapScopeModule.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\Constants.cs" />
    <Compile Include="Helpers\QuidcoHelper.cs" />
    <Compile Include="Helpers\StringExtensionMethods.cs" />
    <Compile Include="Models\ChangePassword\ChangePasswordModel.cs" />
    <Compile Include="Models\Login\LoginModel.cs" />
    <Compile Include="Models\Login\LoginResponse.cs" />
    <Compile Include="Models\NewMember\NewMemberResponse.cs" />
    <Compile Include="Models\NewMember\NewMemberModel.cs" />
    <Compile Include="Models\ResetPassword\ResetPasswordModel.cs" />
    <Compile Include="Models\SetPassword\SetPasswordModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\MembershipService.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Data\AspNetMembershipModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>AspNetMembershipModel.edmx</DependentUpon>
      <LastGenOutput>AspNetMembershipModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Data\AspNetMembershipModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>AspNetMembershipModel.edmx</DependentUpon>
      <LastGenOutput>AspNetMembershipModel.cs</LastGenOutput>
    </Content>
    <Content Include="Data\ClubsModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ClubsModel.edmx</DependentUpon>
      <LastGenOutput>ClubsModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Data\ClubsModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ClubsModel.edmx</DependentUpon>
      <LastGenOutput>ClubsModel.cs</LastGenOutput>
    </Content>
    <Content Include="Data\LegacyCmsModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>LegacyCmsModel.edmx</DependentUpon>
      <LastGenOutput>LegacyCmsModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Data\LegacyCmsModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>LegacyCmsModel.edmx</DependentUpon>
      <LastGenOutput>LegacyCmsModel.cs</LastGenOutput>
    </Content>
    <Content Include="Data\LoggingModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>LoggingModel.edmx</DependentUpon>
      <LastGenOutput>LoggingModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Data\LoggingModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>LoggingModel.edmx</DependentUpon>
      <LastGenOutput>LoggingModel.cs</LastGenOutput>
    </Content>
    <Content Include="Data\MemberProfileModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>MemberProfileModel.edmx</DependentUpon>
      <LastGenOutput>MemberProfileModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Data\MemberProfileModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>MemberProfileModel.edmx</DependentUpon>
      <LastGenOutput>MemberProfileModel.cs</LastGenOutput>
    </Content>
    <Content Include="Global.asax" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Data\AspNetMembershipModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>AspNetMembershipModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\AspNetMembershipModel.edmx.diagram">
      <DependentUpon>AspNetMembershipModel.edmx</DependentUpon>
    </Content>
    <Content Include="config\live\appsettings.config" />
    <Content Include="config\live\client.config" />
    <Content Include="config\live\connectionstrings.config" />
    <Content Include="config\live\exceptionless.config" />
    <Content Include="config\test\appsettings.config" />
    <Content Include="config\test\client.config" />
    <Content Include="config\test\connectionstrings.config" />
    <Content Include="config\test\exceptionless.config" />
    <EntityDeploy Include="Data\LegacyCmsModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>LegacyCmsModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\LegacyCmsModel.edmx.diagram">
      <DependentUpon>LegacyCmsModel.edmx</DependentUpon>
    </Content>
    <EntityDeploy Include="Data\LoggingModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>LoggingModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\LoggingModel.edmx.diagram">
      <DependentUpon>LoggingModel.edmx</DependentUpon>
    </Content>
    <EntityDeploy Include="Data\ClubsModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>ClubsModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\ClubsModel.edmx.diagram">
      <DependentUpon>ClubsModel.edmx</DependentUpon>
    </Content>
    <EntityDeploy Include="Data\MemberProfileModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>MemberProfileModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\MemberProfileModel.edmx.diagram">
      <DependentUpon>MemberProfileModel.edmx</DependentUpon>
    </Content>
    <None Include="NLog.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\TeamCity.pubxml" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55742</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44316/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>