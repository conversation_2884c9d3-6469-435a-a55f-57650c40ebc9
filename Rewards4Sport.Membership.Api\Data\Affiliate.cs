//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Rewards4Sport.Membership.Api.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Affiliate
    {
        public int affiliateID { get; set; }
        public Nullable<int> clubID { get; set; }
        public string applicationName { get; set; }
        public string affiliateUsername { get; set; }
        public string affiliatePassword { get; set; }
        public string affiliateDescription { get; set; }
        public System.DateTime affiliateDate { get; set; }
        public bool affiliateTest { get; set; }
        public bool affiliateSuppress { get; set; }
        public bool affiliateDisplayInAdminSystem { get; set; }
        public Nullable<decimal> affiliatePointsPerPoundEarningRate { get; set; }
        public Nullable<decimal> affiliatePointsPerPoundRedeemRate { get; set; }
        public Nullable<int> affiliateTransactionPointsCap { get; set; }
        public Nullable<int> affiliateMemberPointsCap { get; set; }
        public Nullable<bool> affiliateMarketingOptIn { get; set; }
        public bool affiliateDisableMemberRegistration { get; set; }
        public bool affiliatePointsStatusApplication { get; set; }
    }
}
