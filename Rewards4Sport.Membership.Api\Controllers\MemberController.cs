﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;
using System.Web.UI.WebControls;
using BO2016;
using ENC2016;
using Newtonsoft.Json;
using NLog;
using Rewards4Sport.Membership.Api.Data;
using Rewards4Sport.Membership.Api.Helpers;
using Rewards4Sport.Membership.Api.Models;
using Rewards4Sport.Membership.Api.Models.ChangePassword;
using Rewards4Sport.Membership.Api.Models.ResetPassword;
using Rewards4Sport.Membership.Api.Models.SetPassword;
using Rewards4Sport.Membership.Api.Services;

namespace Rewards4Sport.Membership.Api.Controllers
{
    public class MemberController : Controller
    {
        private readonly MembershipEntities1 _memberDb;
        private readonly LoggingEntities _loggingDb;
        private readonly ClubsEntities _clubsEntities;
        private readonly MemberProfileEntities _memberProfileEntities;
        private ILogger log = LogManager.GetCurrentClassLogger();

        public MemberController(MembershipEntities1 memberDb, LoggingEntities loggingDb, ClubsEntities clubsEntities, MemberProfileEntities memberProfileEntities)
        {
            _memberDb = memberDb;
            _loggingDb = loggingDb;
            _clubsEntities = clubsEntities;
            _memberProfileEntities = memberProfileEntities;
        }

        [Route("member/warmup")]
        public ActionResult Warmup()
        {
            return Json("Ok", JsonRequestBehavior.AllowGet );
        }

        [HttpPost]
        [Route("member/validatecredentials")]
        public async Task<ActionResult> ValidateCredentials(LoginModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            var loginResponse = await membershipService.Login(model);

            var hash = SHA256.ComputeHash(model.Username);

            log.Info($" - Validate credentials for member {hash} on {model.Application} clubId {model.ClubId} was {loginResponse != null}");

            if (loginResponse == null)
                return new HttpNotFoundResult();

            return Json(loginResponse, "application/json");
        }
        
        [HttpPost]
        [Route("member/create")]
        public ActionResult Create(NewMemberModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            log.Info($"Create account for {model.Forename} {model.Surname} on {model.Application} from {model.RegistrationPageUrl}");

            var newMember = membershipService.CreateNewMember(model, true);

            log.Info($" - Created account for member {model.Application} clubId {model.ClubId} was ok");

            return Json(newMember, "application/json");
        }

        [HttpPost]
        [Route("member/createunactivated")]
        public ActionResult CreateUnactivated(NewMemberModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            log.Info($"Create account for {model.Forename} {model.Surname} on {model.Application} from {model.RegistrationPageUrl}");

            if(model.MobileNumber == null)
                model.MobileNumber = string.Empty;

            var newMember = membershipService.CreateNewMember(model, false);

            log.Info($" - Created account for member {model.Application} clubId {model.ClubId} was ok");

            return Json(newMember, "application/json");
        }

        [HttpPost]
        [Route("member/changepassword")]
        public ActionResult ChangePassword(ChangePasswordModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            var newMember = membershipService.ChangePassword(model);

            return Json(newMember, "application/json");
        }

        [HttpPost]
        [Route("member/resetpassword")]
        public ActionResult ResetPassword(ResetPasswordModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            var result = membershipService.ResetPassword(model);

            return Json(result, "application/json");
        }

        [HttpPost]
        [Route("member/setpassword")]
        public ActionResult SetPassword(SetPasswordModel model)
        {
            var membershipService = new MembershipService(_memberDb, _loggingDb, _clubsEntities, _memberProfileEntities);

            var result = membershipService.SetPassword(model);

            return Json(result, "application/json");
        }

    }
}
