trigger:
- none

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Debug'
  publishFolder: '$(Build.ArtifactStagingDirectory)\published_app'

steps:
- task: NuGetToolInstaller@1
  displayName: 'Install NuGet'

- task: NuGetAuthenticate@1
  displayName: 'Authenticate NuGet'
  inputs:
    nuGetServiceConnections: 'Hangfire Pro'

- task: NuGetCommand@2
  displayName: 'NuGet Restore'
  inputs:
    restoreSolution: $(solution)
    nugetConfigPath: 'NuGet.config'
    feedsToUse: config

- script: mkdir "$(publishFolder)"
  displayName: 'Create publish folder'
  condition: ne(variables['publishFolder'], '')

- task: MSBuild@1
  displayName: 'Build and Publish Web Application'
  inputs:
    solution: '**/Rewards4Sport.Membership.Api.csproj'
    msbuildArchitecture: 'x64'
    platform: $(buildPlatform)
    configuration: $(buildConfiguration)
    msbuildArguments: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation=$(publishFolder) /p:OutputPath=$(publishFolder)'

- script: dir "$(publishFolder)"
  displayName: 'List publish folder contents'

- task: ArchiveFiles@2
  displayName: 'Archive Published Web App'
  inputs:
    rootFolderOrFile: '$(publishFolder)\_PublishedWebsites\Rewards4Sport.Membership.Api'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)\$(Build.BuildId).zip'
    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  displayName: 'Publish Artifact: drop'
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)\$(Build.BuildId).zip'
    ArtifactName: 'drop'
    publishLocation: 'Container'

- task: AzureWebApp@1
  displayName: 'Deploy API'
  inputs:
    azureSubscription: 'Microsoft Azure(10a18b90-c406-4a64-ae3d-ab7825161400)'
    appType: 'webApp'
    appName: 'neu-r4g-app-membership-api'
    deployToSlotOrASE: true
    resourceGroupName: 'neu-rsg-app-prd-01'
    slotName: 'testing'
    package: '$(Build.ArtifactStagingDirectory)\$(Build.BuildId).zip'
    deploymentMethod: 'auto'
