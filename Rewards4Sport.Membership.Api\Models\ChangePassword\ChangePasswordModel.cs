﻿using FluentValidation;
using System;

namespace Rewards4Sport.Membership.Api.Models.ChangePassword
{
    public class ChangePasswordModel
    {
        public int MemberId { get; set; }
        public Guid UserId { get; set; }
        public string ApplicationName { get; set; }
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        public string NewPasswordConfirm { get; set; }
        public string IpAddress { get; set; }
        public string SecurityKey { get; set; }
    }

    public class ChangePasswordModelValidator : AbstractValidator<ChangePasswordModel>
    {
        public ChangePasswordModelValidator()
        {
            var passwordRegex = "^(?=.*?[a-z])(?=.*?[0-9]).{8,}$";

            RuleFor(member => member.ApplicationName)
                .Must(application => Constants.ApplicationList.Contains(application.ToLower()));

            RuleFor(member => member.MemberId)
                .GreaterThan(0);

            RuleFor(member => member.SecurityKey)
                .NotNull()
                .MinimumLength(32);

            RuleFor(member => member.NewPassword)
                .NotNull()
                .Matches(passwordRegex)
                .MaximumLength(50);

            RuleFor(member => member.NewPasswordConfirm)
                .NotNull()
                .Matches(passwordRegex)
                .MaximumLength(50);

            RuleFor(member => member.IpAddress)
                .NotNull()
                .MinimumLength(6);

        }
    }

}