﻿<appSettings>
  <add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
  <add key="RegisterCard:DefaultWebsite" value="https://www.rewards4racing.com" />
  <add key="RegisterCard:WebServiceUrl" value="https://cdata.ws.rewards4group.com/managecards/card" />
  <add key="TestApplicationName" value="rewards4racing.com" />
  <add key="BuildNumber" value="{{BUILD_NUMBER}}" />
  <add key="TestLiveWebsite" value="false" />
  <add key="RetailerLink:CheckQuidcoSite" value="true" />
  <add key="Encryption:CacheLengthInSeconds" value="900" />
  <add key="Cache:ShowStatsPassword" value="n24P2hC7ZWetMRuZG8YsdykfBCmr83QaNPGcZZ6bwgG4DQTTdKDav423yatYErrz" />
  <add key="CustomerResolution:SecretKey" value="6!#V8C28U3s7J4eZD6y6SKM632!p7M@4" />
  <add key="Redis:Server" value="neu-tstdev-r4g-redis.redis.cache.windows.net" />
  <add key="Redis:ClientName" value="Rewards4Websites" />
  <add key="Redis:Database" value="test" />
  <add key="Redis:ClearCacheToken" value="sEec9w9kQ5bS3bW38tb2G9ew7eY659XN3Pbu8p88MYke3ubr" />
  <add key="Redis:Password" value="UX0Zr96h3Q6svJpS4l0iuRWd3LpjdvrNrpqTlIhYEeE" />
  <add key="ImageOptimiser:SwitchedOn" value="true" />
  <add key="ImageOptimiser:DefaultQuality" value="75" />
  <add key="SendGrid:EmailApiKey" value="*********************************************************************" />
  <add key="ResetNonActivatedAccount" value="jVNA58276v2z7wcZ"/>
  <add key="SecretKey" value="2Pbmh!6a95x46$%3HS*GPphX9*nyd=eU"/>
</appSettings>