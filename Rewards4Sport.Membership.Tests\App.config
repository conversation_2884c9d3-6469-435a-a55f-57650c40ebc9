﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <connectionStrings>
    <add name="CMS" connectionString="Initial Catalog=CMS;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="Competitions" connectionString="Initial Catalog=Competitions;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="HangfireTaskScheduler" connectionString="Initial Catalog=TaskScheduler_Websites;data source=sqlhosting3.rewards4group.com;user id=web_hangfire; password='sS2joD4jTKJ259%h&gt;SIlrjI7P&lt;7$v1'" />
    <add name="Logging" connectionString="Initial Catalog=Logging;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="MemberProfile" connectionString="Initial Catalog=MemberProfile;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="Membership" connectionString="Initial Catalog=Membership;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="Quidco" connectionString="Initial Catalog=Quidco;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
    <add name="BlogEntities_Moments" connectionString="metadata=res://*/BlogModel.csdl|res://*/BlogModel.ssdl|res://*/BlogModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Blog;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="BlogEntities" connectionString="metadata=res://*/App_Code.BlogModel.csdl|res://*/App_Code.BlogModel.ssdl|res://*/App_Code.BlogModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Blog;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ClubsDataEntities" connectionString="metadata=res://*/ClubsModel.csdl|res://*/ClubsModel.ssdl|res://*/ClubsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Clubs;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ClubsEntities" connectionString="metadata=res://*/App_Code.ClubModel.csdl|res://*/App_Code.ClubModel.ssdl|res://*/App_Code.ClubModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Clubs;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="CMSEntities" connectionString="metadata=res://*/App_Code.CMSModel.csdl|res://*/App_Code.CMSModel.ssdl|res://*/App_Code.CMSModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=CMS;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="CMSEntities2" connectionString="metadata=res://*/Cms_Model.csdl|res://*/Cms_Model.ssdl|res://*/Cms_Model.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=CMS;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="CompetitionsEntities" connectionString="metadata=res://*/CompetitionsModel.csdl|res://*/CompetitionsModel.ssdl|res://*/CompetitionsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Competitions;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="CustomerRelationshipEntities" connectionString="metadata=res://*/CustomerRelationshipModel.csdl|res://*/CustomerRelationshipModel.ssdl|res://*/CustomerRelationshipModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=CustomerRelationship;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ExclusiveOfferEntities" connectionString="metadata=res://*/ExclusiveOffersModel.csdl|res://*/ExclusiveOffersModel.ssdl|res://*/ExclusiveOffersModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=CMS;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="GoalsEntities" connectionString="metadata=res://*/GoalsModel.csdl|res://*/GoalsModel.ssdl|res://*/GoalsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberGoals;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="InsightTeamEntities" connectionString="metadata=res://*/App_Code.InsightTeamModel.csdl|res://*/App_Code.InsightTeamModel.ssdl|res://*/App_Code.InsightTeamModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=InsightTeam;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="LoggingEntities" connectionString="metadata=res://*/LoggingModel.csdl|res://*/LoggingModel.ssdl|res://*/LoggingModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Logging;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MemberGoalsEntities2" connectionString="metadata=res://*/App_Code.MemberGoals.csdl|res://*/App_Code.MemberGoals.ssdl|res://*/App_Code.MemberGoals.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberGoals;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MemberProfEntities" connectionString="metadata=res://*/MemberProfileModel.csdl|res://*/MemberProfileModel.ssdl|res://*/MemberProfileModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberProfile;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MemberProfileEntities" connectionString="metadata=res://*/App_Code.MemberProfileModel.csdl|res://*/App_Code.MemberProfileModel.ssdl|res://*/App_Code.MemberProfileModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberProfile;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MemberProfileEntities2" connectionString="metadata=res://*/App_Code.MemberProfileModel.csdl|res://*/App_Code.MemberProfileModel.ssdl|res://*/App_Code.MemberProfileModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberProfile;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MembershipEntities" connectionString="metadata=res://*/App_Code.MembershipModel.csdl|res://*/App_Code.MembershipModel.ssdl|res://*/App_Code.MembershipModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Membership;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MembershipEntities2" connectionString="metadata=res://*/MembershipModel2.csdl|res://*/MembershipModel2.ssdl|res://*/MembershipModel2.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Membership;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MomentsEntities" connectionString="metadata=res://*/MomentsModel.csdl|res://*/MomentsModel.ssdl|res://*/MomentsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Competitions;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="QuidcoDataEntities" connectionString="metadata=res://*/QuidcoMerchantsModel.csdl|res://*/QuidcoMerchantsModel.ssdl|res://*/QuidcoMerchantsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Quidco;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="QuidcoEntities" connectionString="metadata=res://*/App_Code.QuidcoMerchantModel.csdl|res://*/App_Code.QuidcoMerchantModel.ssdl|res://*/App_Code.QuidcoMerchantModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Quidco;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="WebsiteEmailsEntities" connectionString="metadata=res://*/App_Code.WebsiteEmailsModel.csdl|res://*/App_Code.WebsiteEmailsModel.ssdl|res://*/App_Code.WebsiteEmailsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=WebsiteEmails;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="MembershipEntities1" connectionString="metadata=res://*/Data.AspNetMembershipModel.csdl|res://*/Data.AspNetMembershipModel.ssdl|res://*/Data.AspNetMembershipModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Membership;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <system.web>
    <compilation debug="true" targetFramework="4.7.2" />
    <httpRuntime targetFramework="4.7.2" />
    <membership defaultProvider="Membership">
      <providers>
        <clear />
        <add name="Membership" connectionStringName="Membership" type="Rewards4Sport.Membership.Api.Data.DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" />
      </providers>
    </membership>
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Castle.Core" publicKeyToken="407dd0808d44fbdc" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.Metadata" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>