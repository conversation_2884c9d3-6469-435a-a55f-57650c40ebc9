﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="CMSModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="ApplicationSkins">
          <Key>
            <PropertyRef Name="skinID" />
          </Key>
          <Property Name="skinID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ApplicationName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="skinDomain" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="skinCssPath" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="skinQuidcoID" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="skinQuidcoKey" Type="varchar" MaxLength="250" Nullable="false" />
        </EntityType>
        <EntityContainer Name="CMSModelStoreContainer">
          <EntitySet Name="ApplicationSkins" EntityType="Self.ApplicationSkins" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="CMSModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="ApplicationSkin">
          <Key>
            <PropertyRef Name="skinID" />
          </Key>
          <Property Name="skinID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ApplicationName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="skinDomain" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="skinCssPath" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="skinQuidcoID" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="skinQuidcoKey" Type="String" MaxLength="250" FixedLength="false" Unicode="false" Nullable="false" />
        </EntityType>
        <EntityContainer Name="CMSEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="ApplicationSkins" EntityType="Self.ApplicationSkin" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="CMSModelStoreContainer" CdmEntityContainer="CMSEntities">
          <EntitySetMapping Name="ApplicationSkins">
            <EntityTypeMapping TypeName="CMSModel.ApplicationSkin">
              <MappingFragment StoreEntitySet="ApplicationSkins">
                <ScalarProperty Name="skinID" ColumnName="skinID" />
                <ScalarProperty Name="ApplicationName" ColumnName="ApplicationName" />
                <ScalarProperty Name="skinDomain" ColumnName="skinDomain" />
                <ScalarProperty Name="skinCssPath" ColumnName="skinCssPath" />
                <ScalarProperty Name="skinQuidcoID" ColumnName="skinQuidcoID" />
                <ScalarProperty Name="skinQuidcoKey" ColumnName="skinQuidcoKey" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>