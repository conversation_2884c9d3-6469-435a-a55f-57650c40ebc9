﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using FluentValidation.Results;
using Newtonsoft.Json;

namespace Rewards4Sport.Membership.Api.Models
{
    public class LoginResponse
    {
        public int MemberId { get; set; }
        public string Name { get; set; }
        public string Application { get; set; }
        [JsonIgnore]
        public ValidationResult ValidationResult { get; set; }
    }
}