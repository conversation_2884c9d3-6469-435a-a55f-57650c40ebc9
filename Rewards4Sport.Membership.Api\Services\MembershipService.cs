﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web.Security;
using BO2016;
using ENC2016;
using FluentValidation.Results;
using Hangfire;
using NLog;
//using QUIDCO2016;
using Rewards4Sport.Membership.Api.Data;
using Rewards4Sport.Membership.Api.Helpers;
using Rewards4Sport.Membership.Api.Models;
using Rewards4Sport.Membership.Api.Models.ChangePassword;
using Rewards4Sport.Membership.Api.Models.NewMember;
using Rewards4Sport.Membership.Api.Models.ResetPassword;
using Rewards4Sport.Membership.Api.Models.SetPassword;
using StructureMap.Query;
using UTL2016;
using WebsiteFunctions;
using ConfigurationManager = System.Configuration.ConfigurationManager;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace Rewards4Sport.Membership.Api.Services
{
    public class MembershipService
    {
        private readonly MembershipEntities1 _membersDb;
        private readonly LoggingEntities _loggingEntities;
        private readonly ClubsEntities _clubsEntities;
        private readonly MemberProfileEntities _memberProfileEntities;
        private ILogger log = LogManager.GetCurrentClassLogger();

        public MembershipService(MembershipEntities1 membersDb, LoggingEntities loggingEntities, ClubsEntities clubsEntities, MemberProfileEntities memberProfileEntities)
        {
            _membersDb = membersDb;
            _loggingEntities = loggingEntities;
            _clubsEntities = clubsEntities;
            _memberProfileEntities = memberProfileEntities;
        }

        public async Task<LoginResponse> Login(LoginModel model)
        {
            var validator = new LoginModelValidator();

            var validationResult = validator.Validate(model);

            if (!validationResult.IsValid)
                return null;

            var hashedUsername = ENC2016.SHA256.ComputeHash(model.Username.Trim());
            var hashedUsernameLower = ENC2016.SHA256.ComputeHash(model.Username.ToLower().Trim());

            var usernames = _membersDb.aspnet_Usernames
                .Where(m =>
                    m.ApplicationName == model.Application &&
                    (m.usernameEmail == hashedUsername || m.usernameEmail == hashedUsernameLower) &&
                    m.usernameSuppress == false)
                .ToList();

            if (!usernames.Any())
                return null;

            string loginUsername = usernames.Single().usernameLogin.ToString();

            var loginResult = ValidateLogin(model, loginUsername);

            if (loginResult == false)
            {
                log.Info(" - Login: LoginResult for " + usernames.Single().memberID + " was false");
                return null;
            }

            int memberId = 0;

            memberId = usernames.First().memberID;

            if (memberId == 0)
            {
                log.Info(" - Login: memberId for " + usernames.Single().memberID + " was 0");
                return null;
            }

            var member = await _membersDb.Members.SingleAsync(m => m.memberID == memberId);

            // Make sure this member belongs to this club 
            if (CheckIfClubIdIsCorrect(model, memberId)) 
                return null;

            log.Info(" - Login: member" + memberId + " logged in successfully!");

            return new LoginResponse()
            {
                MemberId = memberId,
                Application = model.Application,
                Name = member.memberForename
            };
        }

        public MembershipProvider GetMembershipProvider(string applicationName)
        {
            MembershipProvider membershipProvider;

            switch (applicationName.ToLower())
            {
                case "rewards4golf":
                    membershipProvider = System.Web.Security.Membership.Providers["rewards4golf"];
                    break;
                case "rewards4cricket":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Cricket"];
                    break;
                case "rewards4football":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Football"];
                    break;
                case "rewards4netball":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Netball"];
                    break;
                case "rewards4racing":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Racing"];
                    break;
                case "rewards4rugby":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Rugby"];
                    break;
                case "rewards4sport":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4Sport"];
                    break;
                case "rewards4rugbyleague":
                    membershipProvider = System.Web.Security.Membership.Providers["Rewards4RugbyLeague"];
                    break;
                default:
                    membershipProvider = System.Web.Security.Membership.Providers["Membership"];
                    break;
            }

            membershipProvider.ApplicationName = applicationName;

            return membershipProvider;
        }

        public bool ValidateLogin(LoginModel model, string loginUsername)
        {
            var membershipProvider = GetMembershipProvider(model.Application);        
            return membershipProvider.ValidateUser(loginUsername, model.Password);
        }

        public bool CheckIfClubIdIsCorrect(LoginModel model, int memberId)
        {
            var oldClubId = model.ClubId;
            var newClubId = model.NewClubId;

            if (oldClubId == newClubId)
                if (oldClubId == 0)
                    return false;

            if (oldClubId >= 1007 && oldClubId != 1011)
                return false;

            var existingClub = _clubsEntities.ClubMembers.SingleOrDefault(m => m.memberID == memberId);

            if (existingClub == null)
            {
                // associate them with the new club
                _clubsEntities.ClubMembers.Add(new ClubMember()
                {
                    clubID = newClubId,
                    memberID = memberId,
                    linkDate = DateTime.Now
                });
                
                _clubsEntities.SaveChanges();
                
                return false;
            }

            if (existingClub.clubID == newClubId)
            {
                return false;
            }

            if (existingClub.clubID == oldClubId)
            {
                // are an old club member
                existingClub.clubID = newClubId;
                _clubsEntities.SaveChanges();
                return false;
            }

            if (existingClub.clubID != oldClubId || existingClub.clubID != newClubId)
            {
                log.Info(" - Login: member " + memberId + " has clubId " + existingClub.clubID + 
                         "  but it's not valid for this login for " + newClubId + " or " + oldClubId);
                return true;
            }

            return false;
        }

        public NewMemberResponse CreateNewMember(NewMemberModel model, bool isMemberActivated)
        {
            // Clean input
            model.Application = model.Application.SetCorrectApplication(); //Rewards4Football"
            model.Username = model.Username.Clean();  // <EMAIL>

            var validator = new NewMemberModelValidator();

            var validationResult = validator.Validate(model);

            if (validationResult.IsValid == false)
            {
                log.Info(" - Create: Could not create account: " + validationResult.ToString());
                return new NewMemberResponse()
                {
                    IsSuccess = false,
                    ValidationResult = validationResult
                };
            }

            var newMemberResponse = CreateUser(model,isMemberActivated);

            return newMemberResponse;
        }

        public NewMemberResponse CreateUser(NewMemberModel model, bool isMemberActivated)
        {
            MembershipCreateStatus status;
            MembershipUser user;

            var member = CreateEmptyMember(model);

            model.Application = model.Application.SetCorrectApplication(); // Rewards4Football
            model.Username = model.Username.Clean(); // <EMAIL>

            var emailAddress = model.Username;
            var mobileNumber = model.MobileNumber;

            var connectionString = ConfigurationManager.ConnectionStrings["Membership"].ConnectionString;

            emailAddress = emailAddress.ToLower();
            mobileNumber = mobileNumber.Replace(" ", "");

            string sHashedEmailAddress = ENC2016.SHA256.ComputeHash(emailAddress); //D22F05A1F8E5170AF1FCD25B08D6CE53E33C0C8B488E37DBBA322326533DE5C2
            string sHashedMobileNo = mobileNumber.Length > 6 ? ENC2016.SHA256.ComputeHash(mobileNumber) : "";

            //var usernames = BO2016.aspnet_Usernames.SearchDatabaseBy_ApplicationName_Email_Suppress(model.Application, sHashedEmailAddress, false, connectionString);

            //if (usernames.Any())
            //{
            //    log.Info(" - Create: Could not create account as " + sHashedEmailAddress + " already exists");
            //    return new NewMemberResponse()
            //    {
            //        IsSuccess = false,
            //        MemberId = 0,
            //        Application = model.Application,
            //        MemberGuid = member.gUserID,
            //        Forename = model.Forename,
            //        ValidationResult = CreateValidationError("username", "Email address already in use")
            //    };
            //}

            string username = Guid.NewGuid().ToString();

            //create an instance of membership provider

            user = GetMembership(model, username, emailAddress, out status);

            if (status != MembershipCreateStatus.Success)
            {
                log.Info(" - Create: Could not create account for " + username + " as status was " + status.ToString());
                return new NewMemberResponse()
                {
                    IsSuccess = false,
                    MemberId = 0,
                    Application = model.Application,
                    MemberGuid = member.gUserID,
                    Forename = model.Forename,
                    ValidationResult = CreateValidationError("username", "Email address already in use")
                };
            }

            if (model.ReferralMemberID > 0)
            {
                member.iReferralMemberID = model.ReferralMemberID;
                member.sSourceID = "Website - Refer a Friend";
                if (model.MemberSourceId.Contains("Ireland"))
                {
                    member.sSourceID = member.sSourceID + " - Ireland";
                }
            }
            else
            {
                member.sSourceID = model.MemberSourceId;
            }

            member.gUserID = new Guid(user.ProviderUserKey.ToString());
            member.iID = member.InsertIntoDatabase(connectionString);

            new BO2016.aspnet_Usernames()
            {
                bSuppress = false,
                gUserID = member.gUserID,
                dDate = DateTime.Now,
                iMemberID = member.iID,
                sApplicationName = model.Application.SetCorrectApplication(),
                sEmail = sHashedEmailAddress,
                sMobile = sHashedMobileNo,
                gLogin = new Guid(username),
            }.InsertIntoDatabase(connectionString);

            if (isMemberActivated)
            {
                LogRegistered(member.iID, model.RegistrationPageUrl, model.RemoteIpAddress);
                UpdatePasswordSetDateOnMemberAccount(member.gUserID);
                SetFullyActivated(member.iID);
                SetTermsVersionToLatest(member.iID, model.MemberTermsVersion);
            }

            _membersDb.SaveChanges();

            if (isMemberActivated)
            {
                LogAcceptedTermsAndConditions(member.iID, model.RegistrationPageUrl, model.RemoteIpAddress);
            }

            SetClubAndTeam(member.iID, model.Application, model.ClubId, model.TeamName);

            log.Info(" - Create: Member " + member.iID + " was created successfully");

            return new NewMemberResponse()
            {
                IsSuccess = true,
                MemberId = member.iID,
                ValidationResult = null,
                Forename = model.Forename,
                Application = model.Application,
                MemberGuid = member.gUserID,
            };
        }

        public MembershipUser GetMembership(NewMemberModel model, string username, string emailAddress, out MembershipCreateStatus status)
        {
            var membershipProvider = GetMembershipProvider(model.Application);
            return membershipProvider.CreateUser(
                username,
                model.Password,
                AES256.Encrypt(emailAddress, username),
                null,
                null,
                true,
                Guid.NewGuid(),
                out status);
        }

        private void SetClubAndTeam(int memberIId, string application, int clubId, string teamName)
        {
            if (clubId == 0)
                return;

            SetClub(memberIId, application, clubId, DateTime.Now);
            SetTeam(memberIId, application, teamName, DateTime.Now);
        }

        [Display(Name = "Set club for {0}")]
        private void SetClub(int memberId, string application, int clubId, DateTime date)
        {
            _clubsEntities.ClubMembers.Add(new ClubMember()
            {
                linkDate = DateTime.Now,
                clubID = clubId,
                memberID = memberId
            });

            _clubsEntities.SaveChanges();
        }

        [Display(Name = "Set team for {0}")]
        private void SetTeam(int memberId, string application, string teamName, DateTime date)
        {
            var previousTeams = _memberProfileEntities
                .TeamSupporters
                .Where(s => s.memberID == memberId);

            if (previousTeams.Any())
                foreach (var team in previousTeams)
                    team.supportedSuppress = true;

            var teamRecord = _memberProfileEntities.Teams
                            .SingleOrDefault(m =>
                                m.applicationName == application &&
                                m.teamName == teamName &&
                                m.teamSuppress == false);

            var teamId = 0;
            if (teamRecord != null)
                teamId = teamRecord.teamID;

            var nationalTeamSupporter = new TeamSupporter()
            {
                memberID = memberId,
                teamID = teamId,
                supportedDate = date,
                supportedSuppress = false
            };

            _memberProfileEntities.TeamSupporters.Add(nationalTeamSupporter);

            _memberProfileEntities.SaveChanges();
        }

        private Members CreateEmptyMember(NewMemberModel model)
        {
            return new Members()
            {
                bPremium = false,
                bUnsubscribe = false,
                dDob = new DateTime(1900, 1, 1),
                dUnsubscribeDate = new DateTime(1900, 1, 1),
                gEmailVerification = Guid.NewGuid(),
                iAffiliateID = 0,
                iGenderID = 3,
                iMarketingID = 1,
                iQuidcoID = 0,
                iReferralMemberID = 0,
                iTitleID = 1,
                iTypeID = 1,
                sAddress1 = "",
                sAddress2 = "",
                sAddress3 = "",
                sAddress4 = "",
                sAddress5 = "",
                sForename = model.Forename,
                sPostcode = "",
                sReferralCode = "",
                sSourceID = "",
                sSurname = string.Empty,
                bActivated = false
            };
        }

        private ValidationResult CreateValidationError(string property, string message)
        {
            var failure = new List<ValidationFailure>()
            {
                new ValidationFailure(property, message)
            };
            return new ValidationResult(failure);
        }

        private void LogRegistered(int memberId, string url, string remoteIpAddress)
        {
            int memberRegisteredTypeId = 32;
            BackgroundJob.Enqueue(() =>
                _loggingEntities.LogMemberAction(memberId, memberRegisteredTypeId, url, remoteIpAddress, 0,
                    DateTime.Now));
        }
        private void LogAcceptedTermsAndConditions(int memberId, string url, string remoteIpAddress)
        {
            int memberAcceptedTermsAndConditions = 7;

            BackgroundJob.Enqueue(() =>
                _loggingEntities.LogMemberAction(
                    memberId,
                    memberAcceptedTermsAndConditions,
                    url,
                    remoteIpAddress,
                    0,
                    DateTime.Now));

            int memberAcceptedTermsAndConditionsOnWebsite = 55;

            BackgroundJob.Enqueue(() =>
                _loggingEntities.LogMemberAction(
                    memberId,
                    memberAcceptedTermsAndConditionsOnWebsite,
                    url,
                    remoteIpAddress,
                    0,
                    DateTime.Now));
        }

        //private void RegisterMemberWithQuidco(string application, int memberId)
        //{
        //    log.Info($"QUIDCO: Attempting to create account for {memberId}");
        //    ApplicationSkins skin = QuidcoHelper.GetQuidcoAccount(application);
            
        //    log.Info($"QUIDCO: Got skin info from db with rowId{skin.iID}");
            
        //    // be aware that partners may be testing this - make sure that the quidcid is 0 before retrying
        //    var password = MembershipManager.CreateRandomPassword(10);

        //    try
        //    {
        //        log.Info($"QUIDCO: Attempting to create Quidco account for {memberId} on {application}");

        //        UserManagement client = new UserManagement(skin.sQuidcoID, skin.sQuidcoKey);
        //        var memberQuidcoId = client.RegisterUser($"{memberId}@rewards4group.com", password, memberId);
                
        //        log.Info($"QUIDCO: Successfully created account for {memberId} on {application}");
                
        //        var member = _membersDb.Members.Single(m => m.memberID == memberId);
        //        member.quidcoID = memberQuidcoId;
        //        _membersDb.SaveChanges();
                
        //        log.Info($"QUIDCO: Successfully updated members quidcoId for {memberId} on {application}");
        //    }
        //    catch (Exception e)
        //    {
        //        log.Error($"QUIDCO ERROR: Could not create account for {memberId} on {application} with Quidco. Error message is: {e.Message}");
        //    }
        //}

        [Display(Name = "Update password date for {0}")]
        public void UpdatePasswordSetDateOnMemberAccount(Guid userId)
        {
            var member = _membersDb.aspnet_Membership.SingleOrDefault(m => m.UserId == userId);

            if (member == null)
                return;

            member.LastPasswordChangedDate = member.CreateDate.AddSeconds(1);
        }

        [Display(Name = "Set {0} fully activated")]
        public void SetFullyActivated(int memberId)
        {
            var member = _membersDb.Members.SingleOrDefault(m => m.memberID == memberId);

            if (member == null)
                return;

            member.memberActivated = true;
        }

        [Display(Name = "Set {0} fully activated")]
        public void SetTermsVersionToLatest(int memberId, decimal memberTermsVersion)
        {
            var member = _membersDb.Members.SingleOrDefault(m => m.memberID == memberId);

            if (member == null)
                return;

            member.memberTermsVersion = memberTermsVersion;
        }

        public bool ChangePassword(ChangePasswordModel model)
        {
            if (ChangePasswordValidate(model) == false)
                return false;

            var membershipProvider = GetMembershipProvider(model.ApplicationName);

            var member = _membersDb.aspnet_Usernames.FirstOrDefault(m => m.memberID == model.MemberId);

            if (member == null)
                return false;

            if (member.UserID != model.UserId)
                return false;

            var user = membershipProvider.GetUser(model.UserId,true);

            if (user.IsLockedOut && user.PasswordQuestion == null)
            {
                log.Info(" - ChangePassword: member " + model.MemberId + " is locked out");
                user.UnlockUser();
            }
            else if (user.IsLockedOut)
            {
                return false;
            }

            if (!user.IsApproved)
                user.IsApproved = true;

            user.Comment = "n";
            membershipProvider.UpdateUser(user);

            var isSuccess = user.ChangePassword(model.CurrentPassword, model.NewPassword);

            log.Info(" - ChangePassword: Updated password for member: " + member.memberID);

            return isSuccess;
        }

        private bool ChangePasswordValidate(ChangePasswordModel model)
        {
            var validator = new ChangePasswordModelValidator();

            var validationResult = validator.Validate(model);

            if (validationResult.IsValid == false)
                return false;

            if (model.SecurityKey != ConfigurationManager.AppSettings["SecretKey"])
                return false;

            return true;
        }

        public bool ResetPassword(ResetPasswordModel model)
        {
            if (model.SecurityKey != ConfigurationManager.AppSettings["SecretKey"])
                return false;

            var member = _membersDb.aspnet_Usernames.FirstOrDefault(m => m.memberID == model.MemberId);

            if (member == null)
                return false;

            if (member.UserID != model.UserId)
                return false;

            var membershipProvider = GetMembershipProvider(model.ApplicationName);

            var user = membershipProvider.GetUser(member.UserID, true);

            if (user.IsLockedOut && user.PasswordQuestion == null)
                user.UnlockUser();
            else if (user.IsLockedOut)
                return false;

            if (!user.IsApproved)
                user.IsApproved = true;

            user.Comment = "n";
            membershipProvider.UpdateUser(user);

            bool result = user.ChangePassword(user.ResetPassword(), model.NewPassword);

            log.Info(" - ResetPassword: Reset password for member " + user.UserName);

            return result;
        }

        public bool SetPassword(SetPasswordModel model)
        {
            if (model.SecurityKey != ConfigurationManager.AppSettings["SecretKey"])
                return false;

            var member = _membersDb.aspnet_Usernames.FirstOrDefault(m => m.memberID == model.MemberId);
            
            if (member == null)
            {
                log.Info(" - SetPassword: Could not find member " + model.MemberId);
                return false;
            }

            if (member.UserID != model.UserId)
                return false;

            var membershipProvider = GetMembershipProvider(model.ApplicationName);

            var user = membershipProvider.GetUser(member.UserID, true);

            if (user.IsLockedOut && user.PasswordQuestion == null)
            {
                user.UnlockUser();
            }
            else if (user.IsLockedOut)
            {
                return false;
            }

            if (!user.IsApproved)
            {
                user.IsApproved = true;
            }

            user.Comment = "n";
            membershipProvider.UpdateUser(user);

            var result = user.ChangePassword(user.ResetPassword(), model.NewPassword.Trim());

            log.Info(" - SetPassword: Set ok for member " + model.MemberId);

            return result;
        }
    }
}