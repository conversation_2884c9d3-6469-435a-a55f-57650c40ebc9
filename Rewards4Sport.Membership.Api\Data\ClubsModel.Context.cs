﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Rewards4Sport.Membership.Api.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class ClubsEntities : DbContext
    {
        public ClubsEntities()
            : base("name=ClubsEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<ClubApplication> ClubApplications { get; set; }
        public virtual IDbSet<ClubMember> ClubMembers { get; set; }
        public virtual DbSet<PartnerClub> PartnerClubs { get; set; }
    }
}
