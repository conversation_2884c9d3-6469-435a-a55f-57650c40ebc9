﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="exceptionless" type="Exceptionless.ExceptionlessSection, Exceptionless" />
  </configSections>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <exceptionless apiKey="API_KEY_HERE" />
  <system.web>
    <customErrors mode="Off">
      <error statusCode="404" redirect="~/404" />
    </customErrors>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Security, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <add assembly="System.Data.Entity.Design, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        <add assembly="System.Runtime.Caching, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.IO.Compression.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.ComponentModel.Composition, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Numerics, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
        <add assembly="System.IO.Compression, Version=4.2.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
      </assemblies>
      <buildProviders>
        <add extension=".edmx" type="System.Data.Entity.Design.AspNet.EntityDesignerBuildProvider" />
      </buildProviders>
    </compilation>
    <httpRuntime targetFramework="4.5" />
    <authentication mode="Forms">
      <forms name=".ASPXAUTH" loginUrl="/" protection="All" timeout="120" requireSSL="false" slidingExpiration="true" cookieless="UseDeviceProfile" enableCrossAppRedirects="false">
        <credentials passwordFormat="SHA1" />
      </forms>
    </authentication>
    <membership defaultProvider="Membership">
      <providers>
        <clear />
        <add name="Membership" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Racing" />
        <add name="rewards4golf" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="rewards4golf" />
        <add name="rewards4cricket" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Cricket" />
        <add name="rewards4football" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Football" />
        <add name="rewards4netball" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Netball" />
        <add name="rewards4racing" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Racing" />
        <add name="rewards4rugby" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Rugby" />
        <add name="rewards4sport" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4Sport" />
        <add name="rewards4rugbyleague" connectionStringName="Membership" type="DAL2016.SecureMembershipProvider" requiresQuestionAndAnswer="false" enablePasswordRetrieval="false" passwordFormat="Hashed" minRequiredPasswordLength="2" minRequiredNonalphanumericCharacters="0" requiresUniqueEmail="false" applicationName="Rewards4RugbyLeague" />
      </providers>
    </membership>
    <pages />
    <globalization culture="en-GB" uiCulture="en-GB" />
  </system.web>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="50000000" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <connectionStrings configSource=".\config\test\connectionstrings.config" />
  <appSettings configSource=".\config\test\appsettings.config" />
  <system.webServer>
    <modules>
      <remove name="ExceptionlessModule" />
      <add name="ExceptionlessModule" type="Exceptionless.Mvc.ExceptionlessModule, Exceptionless.Mvc" />
    </modules>
    <httpErrors>
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" prefixLanguageFilePath="" path="/404" responseMode="ExecuteURL" />
    </httpErrors>
    <urlCompression doStaticCompression="false" />
    <caching>
      <profiles>
        <add extension=".js" policy="CacheForTimePeriod" kernelCachePolicy="DontCache" duration="00:30:00" />
        <add extension=".png" policy="CacheForTimePeriod" kernelCachePolicy="DontCache" duration="00:02:00" />
        <add extension=".ttf" policy="CacheForTimePeriod" kernelCachePolicy="DontCache" duration="00:30:00" />
        <add extension=".css" policy="CacheForTimePeriod" kernelCachePolicy="DontCache" duration="00:30:00" />
      </profiles>
    </caching>
  <handlers>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" />
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers></system.webServer>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v11.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.Algorithms" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.X509Certificates" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.2" newVersion="4.1.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.Primitives" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" />
        <bindingRedirect oldVersion="0.0.0.0-4.6.0.0" newVersion="4.6.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.1" newVersion="4.0.2.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.Metadata" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="soapSoap">
          <security mode="Transport" />
        </binding>
        <binding name="soapSoap1" />
      </basicHttpBinding>
    </bindings>
    <client configSource=".\config\test\client.config" />
  </system.serviceModel>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    </compilers>
  </system.codedom>
</configuration>