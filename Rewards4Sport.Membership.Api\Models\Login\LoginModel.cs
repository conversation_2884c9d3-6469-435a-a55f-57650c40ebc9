﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using FluentValidation;
using FluentValidation.Validators;

namespace Rewards4Sport.Membership.Api.Models
{
    public class LoginModel
    {
        public string Username { get; set; }
        public string Password { get; set; }
        public string Application { get; set; }
        public int ClubId { get; set; }
        public int NewClubId { get; set; }
    }

    public class LoginModelValidator : AbstractValidator<LoginModel>
    {
        public LoginModelValidator()
        {
            RuleFor(member => member.Username)
                .NotNull()
                .EmailAddress(EmailValidationMode.Net4xRegex);

            RuleFor(member => member.Password)
                .NotNull()
                .Matches("^(?=.*?[a-z])(?=.*?[0-9]).{8,}$")
                .MaximumLength(50);
        }
    }
}
