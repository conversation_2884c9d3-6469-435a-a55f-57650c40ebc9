﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using Exceptionless;
using Hangfire;
//using Hangfire.Pro.Redis;
using NLog;

namespace Rewards4Sport.Membership.Api
{
    public class MvcApplication : System.Web.HttpApplication
    {
        private static Logger log = LogManager.GetCurrentClassLogger();

        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
        }
        
        protected void Application_Error(object sender, EventArgs e)
        {
            if (Server.GetLastError() == null)
            {
                log.Error("Error was found but GetLastError is null");
                return;
            }
            
            log.Error(Server.GetLastError());
            var client = Exceptionless.ExceptionlessClient.Default;
            client.SubmitException(Server.GetLastError());
        }
    }
}
