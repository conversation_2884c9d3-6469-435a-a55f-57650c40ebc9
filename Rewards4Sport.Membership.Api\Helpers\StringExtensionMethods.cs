﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Rewards4Sport.Membership.Api.Helpers
{
    public static class StringExtensionMethods
    {
        public static string Clean(this string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            return text.Trim().ToLower();
        }

        public static string SetCorrectApplication(this string text)
        {
            text = text.Clean();

            if (text == "rewards4cricket")
                return "Rewards4Cricket";
            else if (text == "rewards4football")
                return "Rewards4Football";
            else if (text == "rewards4golf")
                return "rewards4golf";
            else if (text == "rewards4netball")
                return "Rewards4Netball";
            else if (text == "rewards4racing")
                return "Rewards4Racing";
            else if (text == "rewards4rugby")
                return "Rewards4Rugby";
            else if (text == "rewards4sport")
                return "Rewards4Sport";
            else if (text == "rewards4rugbyleague")
                return "Rewards4RugbyLeague";
            else
                throw new ArgumentException($"Cannot find application for {text}");
        }
    }
}