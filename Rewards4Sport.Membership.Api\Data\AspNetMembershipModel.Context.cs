﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Rewards4Sport.Membership.Api.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class MembershipEntities1 : DbContext
    {
        public MembershipEntities1()
            : base("name=MembershipEntities1")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<aspnet_Applications> aspnet_Applications { get; set; }
        public virtual DbSet<aspnet_Membership> aspnet_Membership { get; set; }
        public virtual DbSet<aspnet_SchemaVersions> aspnet_SchemaVersions { get; set; }
        public virtual DbSet<aspnet_Usernames> aspnet_Usernames { get; set; }
        public virtual DbSet<aspnet_Users> aspnet_Users { get; set; }
        public virtual DbSet<Member> Members { get; set; }
        public virtual DbSet<Affiliate> Affiliates { get; set; }
    
        public virtual int aspnet_Membership_CreateUser(string applicationName, string userName, string password, string passwordSalt, string email, string passwordQuestion, string passwordAnswer, Nullable<bool> isApproved, Nullable<System.DateTime> currentTimeUtc, Nullable<System.DateTime> createDate, Nullable<int> uniqueEmail, Nullable<int> passwordFormat, ObjectParameter userId)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("Password", password) :
                new ObjectParameter("Password", typeof(string));
    
            var passwordSaltParameter = passwordSalt != null ?
                new ObjectParameter("PasswordSalt", passwordSalt) :
                new ObjectParameter("PasswordSalt", typeof(string));
    
            var emailParameter = email != null ?
                new ObjectParameter("Email", email) :
                new ObjectParameter("Email", typeof(string));
    
            var passwordQuestionParameter = passwordQuestion != null ?
                new ObjectParameter("PasswordQuestion", passwordQuestion) :
                new ObjectParameter("PasswordQuestion", typeof(string));
    
            var passwordAnswerParameter = passwordAnswer != null ?
                new ObjectParameter("PasswordAnswer", passwordAnswer) :
                new ObjectParameter("PasswordAnswer", typeof(string));
    
            var isApprovedParameter = isApproved.HasValue ?
                new ObjectParameter("IsApproved", isApproved) :
                new ObjectParameter("IsApproved", typeof(bool));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var createDateParameter = createDate.HasValue ?
                new ObjectParameter("CreateDate", createDate) :
                new ObjectParameter("CreateDate", typeof(System.DateTime));
    
            var uniqueEmailParameter = uniqueEmail.HasValue ?
                new ObjectParameter("UniqueEmail", uniqueEmail) :
                new ObjectParameter("UniqueEmail", typeof(int));
    
            var passwordFormatParameter = passwordFormat.HasValue ?
                new ObjectParameter("PasswordFormat", passwordFormat) :
                new ObjectParameter("PasswordFormat", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_CreateUser", applicationNameParameter, userNameParameter, passwordParameter, passwordSaltParameter, emailParameter, passwordQuestionParameter, passwordAnswerParameter, isApprovedParameter, currentTimeUtcParameter, createDateParameter, uniqueEmailParameter, passwordFormatParameter, userId);
        }
    
        public virtual int aspnet_Membership_FindUsersByEmail(string applicationName, string emailToMatch, Nullable<int> pageIndex, Nullable<int> pageSize)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var emailToMatchParameter = emailToMatch != null ?
                new ObjectParameter("EmailToMatch", emailToMatch) :
                new ObjectParameter("EmailToMatch", typeof(string));
    
            var pageIndexParameter = pageIndex.HasValue ?
                new ObjectParameter("PageIndex", pageIndex) :
                new ObjectParameter("PageIndex", typeof(int));
    
            var pageSizeParameter = pageSize.HasValue ?
                new ObjectParameter("PageSize", pageSize) :
                new ObjectParameter("PageSize", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_FindUsersByEmail", applicationNameParameter, emailToMatchParameter, pageIndexParameter, pageSizeParameter);
        }
    
        public virtual int aspnet_Membership_FindUsersByName(string applicationName, string userNameToMatch, Nullable<int> pageIndex, Nullable<int> pageSize)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameToMatchParameter = userNameToMatch != null ?
                new ObjectParameter("UserNameToMatch", userNameToMatch) :
                new ObjectParameter("UserNameToMatch", typeof(string));
    
            var pageIndexParameter = pageIndex.HasValue ?
                new ObjectParameter("PageIndex", pageIndex) :
                new ObjectParameter("PageIndex", typeof(int));
    
            var pageSizeParameter = pageSize.HasValue ?
                new ObjectParameter("PageSize", pageSize) :
                new ObjectParameter("PageSize", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_FindUsersByName", applicationNameParameter, userNameToMatchParameter, pageIndexParameter, pageSizeParameter);
        }
    
        public virtual int aspnet_Membership_GetAllUsers(string applicationName, Nullable<int> pageIndex, Nullable<int> pageSize)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var pageIndexParameter = pageIndex.HasValue ?
                new ObjectParameter("PageIndex", pageIndex) :
                new ObjectParameter("PageIndex", typeof(int));
    
            var pageSizeParameter = pageSize.HasValue ?
                new ObjectParameter("PageSize", pageSize) :
                new ObjectParameter("PageSize", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_GetAllUsers", applicationNameParameter, pageIndexParameter, pageSizeParameter);
        }
    
        public virtual int aspnet_Membership_GetNumberOfUsersOnline(string applicationName, Nullable<int> minutesSinceLastInActive, Nullable<System.DateTime> currentTimeUtc)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var minutesSinceLastInActiveParameter = minutesSinceLastInActive.HasValue ?
                new ObjectParameter("MinutesSinceLastInActive", minutesSinceLastInActive) :
                new ObjectParameter("MinutesSinceLastInActive", typeof(int));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_GetNumberOfUsersOnline", applicationNameParameter, minutesSinceLastInActiveParameter, currentTimeUtcParameter);
        }
    
        public virtual ObjectResult<aspnet_Membership_GetPassword_Result> aspnet_Membership_GetPassword(string applicationName, string userName, Nullable<int> maxInvalidPasswordAttempts, Nullable<int> passwordAttemptWindow, Nullable<System.DateTime> currentTimeUtc, string passwordAnswer)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var maxInvalidPasswordAttemptsParameter = maxInvalidPasswordAttempts.HasValue ?
                new ObjectParameter("MaxInvalidPasswordAttempts", maxInvalidPasswordAttempts) :
                new ObjectParameter("MaxInvalidPasswordAttempts", typeof(int));
    
            var passwordAttemptWindowParameter = passwordAttemptWindow.HasValue ?
                new ObjectParameter("PasswordAttemptWindow", passwordAttemptWindow) :
                new ObjectParameter("PasswordAttemptWindow", typeof(int));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var passwordAnswerParameter = passwordAnswer != null ?
                new ObjectParameter("PasswordAnswer", passwordAnswer) :
                new ObjectParameter("PasswordAnswer", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<aspnet_Membership_GetPassword_Result>("aspnet_Membership_GetPassword", applicationNameParameter, userNameParameter, maxInvalidPasswordAttemptsParameter, passwordAttemptWindowParameter, currentTimeUtcParameter, passwordAnswerParameter);
        }
    
        public virtual ObjectResult<aspnet_Membership_GetPasswordWithFormat_Result> aspnet_Membership_GetPasswordWithFormat(string applicationName, string userName, Nullable<bool> updateLastLoginActivityDate, Nullable<System.DateTime> currentTimeUtc)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var updateLastLoginActivityDateParameter = updateLastLoginActivityDate.HasValue ?
                new ObjectParameter("UpdateLastLoginActivityDate", updateLastLoginActivityDate) :
                new ObjectParameter("UpdateLastLoginActivityDate", typeof(bool));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<aspnet_Membership_GetPasswordWithFormat_Result>("aspnet_Membership_GetPasswordWithFormat", applicationNameParameter, userNameParameter, updateLastLoginActivityDateParameter, currentTimeUtcParameter);
        }
    
        public virtual ObjectResult<string> aspnet_Membership_GetUserByEmail(string applicationName, string email)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var emailParameter = email != null ?
                new ObjectParameter("Email", email) :
                new ObjectParameter("Email", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("aspnet_Membership_GetUserByEmail", applicationNameParameter, emailParameter);
        }
    
        public virtual ObjectResult<aspnet_Membership_GetUserByName_Result> aspnet_Membership_GetUserByName(string applicationName, string userName, Nullable<System.DateTime> currentTimeUtc, Nullable<bool> updateLastActivity)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var updateLastActivityParameter = updateLastActivity.HasValue ?
                new ObjectParameter("UpdateLastActivity", updateLastActivity) :
                new ObjectParameter("UpdateLastActivity", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<aspnet_Membership_GetUserByName_Result>("aspnet_Membership_GetUserByName", applicationNameParameter, userNameParameter, currentTimeUtcParameter, updateLastActivityParameter);
        }
    
        public virtual ObjectResult<aspnet_Membership_GetUserByUserId_Result> aspnet_Membership_GetUserByUserId(Nullable<System.Guid> userId, Nullable<System.DateTime> currentTimeUtc, Nullable<bool> updateLastActivity)
        {
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(System.Guid));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var updateLastActivityParameter = updateLastActivity.HasValue ?
                new ObjectParameter("UpdateLastActivity", updateLastActivity) :
                new ObjectParameter("UpdateLastActivity", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<aspnet_Membership_GetUserByUserId_Result>("aspnet_Membership_GetUserByUserId", userIdParameter, currentTimeUtcParameter, updateLastActivityParameter);
        }
    
        public virtual int aspnet_Membership_ResetPassword(string applicationName, string userName, string newPassword, Nullable<int> maxInvalidPasswordAttempts, Nullable<int> passwordAttemptWindow, string passwordSalt, Nullable<System.DateTime> currentTimeUtc, Nullable<int> passwordFormat, string passwordAnswer)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var newPasswordParameter = newPassword != null ?
                new ObjectParameter("NewPassword", newPassword) :
                new ObjectParameter("NewPassword", typeof(string));
    
            var maxInvalidPasswordAttemptsParameter = maxInvalidPasswordAttempts.HasValue ?
                new ObjectParameter("MaxInvalidPasswordAttempts", maxInvalidPasswordAttempts) :
                new ObjectParameter("MaxInvalidPasswordAttempts", typeof(int));
    
            var passwordAttemptWindowParameter = passwordAttemptWindow.HasValue ?
                new ObjectParameter("PasswordAttemptWindow", passwordAttemptWindow) :
                new ObjectParameter("PasswordAttemptWindow", typeof(int));
    
            var passwordSaltParameter = passwordSalt != null ?
                new ObjectParameter("PasswordSalt", passwordSalt) :
                new ObjectParameter("PasswordSalt", typeof(string));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var passwordFormatParameter = passwordFormat.HasValue ?
                new ObjectParameter("PasswordFormat", passwordFormat) :
                new ObjectParameter("PasswordFormat", typeof(int));
    
            var passwordAnswerParameter = passwordAnswer != null ?
                new ObjectParameter("PasswordAnswer", passwordAnswer) :
                new ObjectParameter("PasswordAnswer", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_ResetPassword", applicationNameParameter, userNameParameter, newPasswordParameter, maxInvalidPasswordAttemptsParameter, passwordAttemptWindowParameter, passwordSaltParameter, currentTimeUtcParameter, passwordFormatParameter, passwordAnswerParameter);
        }
    
        public virtual int aspnet_Membership_SetPassword(string applicationName, string userName, string newPassword, string passwordSalt, Nullable<System.DateTime> currentTimeUtc, Nullable<int> passwordFormat)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var newPasswordParameter = newPassword != null ?
                new ObjectParameter("NewPassword", newPassword) :
                new ObjectParameter("NewPassword", typeof(string));
    
            var passwordSaltParameter = passwordSalt != null ?
                new ObjectParameter("PasswordSalt", passwordSalt) :
                new ObjectParameter("PasswordSalt", typeof(string));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            var passwordFormatParameter = passwordFormat.HasValue ?
                new ObjectParameter("PasswordFormat", passwordFormat) :
                new ObjectParameter("PasswordFormat", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_SetPassword", applicationNameParameter, userNameParameter, newPasswordParameter, passwordSaltParameter, currentTimeUtcParameter, passwordFormatParameter);
        }
    
        public virtual int aspnet_Membership_UnlockUser(string applicationName, string userName)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_UnlockUser", applicationNameParameter, userNameParameter);
        }
    
        public virtual int aspnet_Membership_UpdateUser(string applicationName, string userName, string email, string comment, Nullable<bool> isApproved, Nullable<System.DateTime> lastLoginDate, Nullable<System.DateTime> lastActivityDate, Nullable<int> uniqueEmail, Nullable<System.DateTime> currentTimeUtc)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var emailParameter = email != null ?
                new ObjectParameter("Email", email) :
                new ObjectParameter("Email", typeof(string));
    
            var commentParameter = comment != null ?
                new ObjectParameter("Comment", comment) :
                new ObjectParameter("Comment", typeof(string));
    
            var isApprovedParameter = isApproved.HasValue ?
                new ObjectParameter("IsApproved", isApproved) :
                new ObjectParameter("IsApproved", typeof(bool));
    
            var lastLoginDateParameter = lastLoginDate.HasValue ?
                new ObjectParameter("LastLoginDate", lastLoginDate) :
                new ObjectParameter("LastLoginDate", typeof(System.DateTime));
    
            var lastActivityDateParameter = lastActivityDate.HasValue ?
                new ObjectParameter("LastActivityDate", lastActivityDate) :
                new ObjectParameter("LastActivityDate", typeof(System.DateTime));
    
            var uniqueEmailParameter = uniqueEmail.HasValue ?
                new ObjectParameter("UniqueEmail", uniqueEmail) :
                new ObjectParameter("UniqueEmail", typeof(int));
    
            var currentTimeUtcParameter = currentTimeUtc.HasValue ?
                new ObjectParameter("CurrentTimeUtc", currentTimeUtc) :
                new ObjectParameter("CurrentTimeUtc", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Membership_UpdateUser", applicationNameParameter, userNameParameter, emailParameter, commentParameter, isApprovedParameter, lastLoginDateParameter, lastActivityDateParameter, uniqueEmailParameter, currentTimeUtcParameter);
        }
    
        public virtual int aspnet_Users_CreateUser(Nullable<System.Guid> applicationId, string userName, Nullable<bool> isUserAnonymous, Nullable<System.DateTime> lastActivityDate, ObjectParameter userId)
        {
            var applicationIdParameter = applicationId.HasValue ?
                new ObjectParameter("ApplicationId", applicationId) :
                new ObjectParameter("ApplicationId", typeof(System.Guid));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var isUserAnonymousParameter = isUserAnonymous.HasValue ?
                new ObjectParameter("IsUserAnonymous", isUserAnonymous) :
                new ObjectParameter("IsUserAnonymous", typeof(bool));
    
            var lastActivityDateParameter = lastActivityDate.HasValue ?
                new ObjectParameter("LastActivityDate", lastActivityDate) :
                new ObjectParameter("LastActivityDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Users_CreateUser", applicationIdParameter, userNameParameter, isUserAnonymousParameter, lastActivityDateParameter, userId);
        }
    
        public virtual int aspnet_Users_DeleteUser(string applicationName, string userName, Nullable<int> tablesToDeleteFrom, ObjectParameter numTablesDeletedFrom)
        {
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            var userNameParameter = userName != null ?
                new ObjectParameter("UserName", userName) :
                new ObjectParameter("UserName", typeof(string));
    
            var tablesToDeleteFromParameter = tablesToDeleteFrom.HasValue ?
                new ObjectParameter("TablesToDeleteFrom", tablesToDeleteFrom) :
                new ObjectParameter("TablesToDeleteFrom", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("aspnet_Users_DeleteUser", applicationNameParameter, userNameParameter, tablesToDeleteFromParameter, numTablesDeletedFrom);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckMemberApplication(Nullable<int> memberID, string applicationName)
        {
            var memberIDParameter = memberID.HasValue ?
                new ObjectParameter("MemberID", memberID) :
                new ObjectParameter("MemberID", typeof(int));
    
            var applicationNameParameter = applicationName != null ?
                new ObjectParameter("ApplicationName", applicationName) :
                new ObjectParameter("ApplicationName", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckMemberApplication", memberIDParameter, applicationNameParameter);
        }
    
        public virtual ObjectResult<GetAffiliateMemberSources_ByAffiliateIDMemberID_Result> GetAffiliateMemberSources_ByAffiliateIDMemberID(Nullable<int> affiliateID, Nullable<int> memberID)
        {
            var affiliateIDParameter = affiliateID.HasValue ?
                new ObjectParameter("affiliateID", affiliateID) :
                new ObjectParameter("affiliateID", typeof(int));
    
            var memberIDParameter = memberID.HasValue ?
                new ObjectParameter("memberID", memberID) :
                new ObjectParameter("memberID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAffiliateMemberSources_ByAffiliateIDMemberID_Result>("GetAffiliateMemberSources_ByAffiliateIDMemberID", affiliateIDParameter, memberIDParameter);
        }
    
        public virtual ObjectResult<GetAffiliateMemberSources_ByAffiliateIDValue_Result> GetAffiliateMemberSources_ByAffiliateIDValue(Nullable<int> affiliateID, string value)
        {
            var affiliateIDParameter = affiliateID.HasValue ?
                new ObjectParameter("affiliateID", affiliateID) :
                new ObjectParameter("affiliateID", typeof(int));
    
            var valueParameter = value != null ?
                new ObjectParameter("Value", value) :
                new ObjectParameter("Value", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAffiliateMemberSources_ByAffiliateIDValue_Result>("GetAffiliateMemberSources_ByAffiliateIDValue", affiliateIDParameter, valueParameter);
        }
    
        public virtual ObjectResult<GetAffiliateMemberSources_ByMemberID_Result> GetAffiliateMemberSources_ByMemberID(Nullable<int> memberID)
        {
            var memberIDParameter = memberID.HasValue ?
                new ObjectParameter("memberID", memberID) :
                new ObjectParameter("memberID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAffiliateMemberSources_ByMemberID_Result>("GetAffiliateMemberSources_ByMemberID", memberIDParameter);
        }
    }
}
