﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="NLog" targetNamespace="http://www.nlog-project.org/schemas/NLog.xsd" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.nlog-project.org/schemas/NLog.xsd">
  <xs:element name="nlog" type="NLogConfiguration" />
  <xs:complexType name="NLogConfiguration">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="extensions" type="NLogExtensions" />
      <xs:element name="include" type="NLogInclude" />
      <xs:element name="variable" type="NLogVariable" />
      <xs:element name="variables" type="NLogVariables" />
      <xs:element name="targets" type="NLogTargets" />
      <xs:element name="rules" type="NLogRules" />
      <xs:element name="time" type="TimeSource" />
    </xs:choice>
    <xs:attribute name="autoReload" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Watch config file for changes and reload automatically.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToConsole" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Print internal NLog messages to the console. Default value is: false</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToConsoleError" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Print internal NLog messages to the console error output. Default value is: false</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogFile" type="xs:string">
      <xs:annotation>
        <xs:documentation>Write internal NLog messages to the specified file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogLevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Log level threshold for internal log messages. Default value is: Info.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="globalThreshold" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Global log level threshold for application log messages. Messages below this level won't be logged.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="throwExceptions" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Throw an exception when there is an internal error. Default value is: false. Not recommend to set to true in production!</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="throwConfigExceptions" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Throw an exception when there is a configuration error. If not set, determined by throwExceptions.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="keepVariablesOnReload" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Gets or sets a value indicating whether Variables should be kept on configuration reload. Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToTrace" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Write internal NLog messages to the System.Diagnostics.Trace. Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogIncludeTimestamp" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Write timestamps for internal NLog messages. Default value is: true.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="useInvariantCulture" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Use InvariantCulture as default culture instead of CurrentCulture.  Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parseMessageTemplates" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Perform message template parsing and formatting of LogEvent messages (true = Always, false = Never, empty = Auto Detect). Default value is: empty.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogTargets">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="default-wrapper" type="WrapperTargetBase" />
      <xs:element name="default-target-parameters" type="Target" />
      <xs:element name="target" type="Target" />
      <xs:element name="wrapper-target" type="WrapperTargetBase" />
      <xs:element name="compound-target" type="CompoundTargetBase" />
    </xs:choice>
    <xs:attribute name="async" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Make all targets within this section asynchronous (creates additional threads but the calling thread isn't blocked by any target writes).</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogRules">
    <xs:sequence minOccurs="0" maxOccurs="unbounded">
      <xs:element name="logger" type="NLogLoggerRule" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NLogExtensions">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="add" type="NLogExtensionsAdd" />
    </xs:choice>
  </xs:complexType>
  <xs:complexType name="NLogExtensionsAdd">
    <xs:attribute name="prefix" type="xs:string">
      <xs:annotation>
        <xs:documentation>Prefix for targets/layout renderers/filters/conditions loaded from this assembly.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="assemblyFile" type="xs:string">
      <xs:annotation>
        <xs:documentation>Load NLog extensions from the specified file (*.dll)</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="assembly" type="xs:string">
      <xs:annotation>
        <xs:documentation>Load NLog extensions from the specified assembly. Assembly name should be fully qualified.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogLoggerRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="filters" type="NLogFilters" />
    </xs:choice>
    <xs:attribute name="name" use="optional">
      <xs:annotation>
        <xs:documentation>Filter on the name of the logger. May include wildcard characters ('*' or '?').</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="levels" type="NLogLevelList">
      <xs:annotation>
        <xs:documentation>Comma separated list of levels that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="minlevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Minimum level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="maxlevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Maximum level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="level" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="writeTo" type="NLogTargetIDList">
      <xs:annotation>
        <xs:documentation>Comma separated list of target names.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="final" type="xs:boolean" default="false">
      <xs:annotation>
        <xs:documentation>Ignore further rules if this one matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="enabled" type="xs:boolean" default="true">
      <xs:annotation>
        <xs:documentation>Enable this rule. Note: disabled rules aren't available from the API.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ruleName" type="xs:string" use="optional">
      <xs:annotation>
        <xs:documentation>Rule identifier to allow rule lookup with Configuration.FindRuleByName and Configuration.RemoveRuleByName.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="finalMinLevel" type="NLogLevel" use="optional">
      <xs:annotation>
        <xs:documentation>Loggers matching will be restricted to specified minimum level for following rules.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogFilters">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="when" type="when" />
      <xs:element name="whenContains" type="whenContains" />
      <xs:element name="whenEqual" type="whenEqual" />
      <xs:element name="whenNotContains" type="whenNotContains" />
      <xs:element name="whenNotEqual" type="whenNotEqual" />
      <xs:element name="whenRepeated" type="whenRepeated" />
    </xs:choice>
    <xs:attribute name="defaultAction" type="FilterResult">
      <xs:annotation>
        <xs:documentation>Default action if none of the filters match.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:simpleType name="NLogLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Off" />
      <xs:enumeration value="Trace" />
      <xs:enumeration value="Debug" />
      <xs:enumeration value="Info" />
      <xs:enumeration value="Warn" />
      <xs:enumeration value="Error" />
      <xs:enumeration value="Fatal" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="LineEndingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="CRLF" />
      <xs:enumeration value="CR" />
      <xs:enumeration value="LF" />
      <xs:enumeration value="None" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLogLevelList">
    <xs:restriction base="xs:string">
      <xs:pattern value="(|Trace|Debug|Info|Warn|Error|Fatal)(,(Trace|Debug|Info|Warn|Error|Fatal))*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLogInclude">
    <xs:attribute name="file" type="SimpleLayoutAttribute" use="required">
      <xs:annotation>
        <xs:documentation>Name of the file to be included. You could use * wildcard. The name is relative to the name of the current config file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreErrors" type="xs:boolean" use="optional" default="false">
      <xs:annotation>
        <xs:documentation>Ignore any errors in the include file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogVariable">
    <xs:choice minOccurs="0" maxOccurs="1">
      <xs:element name="value" minOccurs="0" maxOccurs="1" type="xs:string">
        <xs:annotation>
          <xs:documentation>Variable value. Note, the 'value' attribute has precedence over this one.</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout">
        <xs:annotation>
          <xs:documentation>Layout type variable value. Note, the 'value' attribute has precedence over this one.</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:choice>
    <xs:attribute name="name" type="xs:string" use="required">
      <xs:annotation>
        <xs:documentation>Variable name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="value" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Variable value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogVariables">
    <xs:sequence minOccurs="0" maxOccurs="unbounded">
      <xs:element name="variable" type="NLogVariable" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="NLogTargetIDList">
    <xs:restriction base="xs:string">
      <xs:pattern value="(|([a-zA-Z][a-zA-Z0-9_\-]*))(,([a-zA-Z][a-zA-Z0-9_\-]*))*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Target" abstract="true"></xs:complexType>
  <xs:complexType name="TargetRef">
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="WrapperTargetBase" abstract="true">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="target" type="Target" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target" type="WrapperTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target" type="CompoundTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CompoundTargetBase" abstract="true">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="target" type="Target" minOccurs="1" maxOccurs="unbounded" />
          <xs:element name="wrapper-target" type="WrapperTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target" type="CompoundTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Filter" abstract="true"></xs:complexType>
  <xs:complexType name="TimeSource" abstract="true"></xs:complexType>
  <xs:simpleType name="SimpleLayoutAttribute">
    <xs:restriction base="xs:string">
      <xs:pattern value=".*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="Condition">
    <xs:restriction base="xs:string">
      <xs:minLength value="1" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="AsyncWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="overflowAction" type="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="queueLimit" type="xs:integer" minOccurs="0" maxOccurs="1" default="10000" />
          <xs:element name="batchSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="200" />
          <xs:element name="forceLockingQueue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="fullBatchSizeWriteLimit" type="xs:integer" minOccurs="0" maxOccurs="1" default="5" />
          <xs:element name="timeToSleepBetweenBatches" type="xs:integer" minOccurs="0" maxOccurs="1" default="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="overflowAction" type="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to be taken when the lazy writer thread request queue count exceeds the set limit.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="queueLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Limit on the number of requests in the lazy writer thread request queue.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="batchSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of log events that should be processed in a batch by the lazy writer thread.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceLockingQueue" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to use the locking queue, instead of a lock-free concurrent queue</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fullBatchSizeWriteLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of batches of P:NLog.Targets.Wrappers.AsyncTargetWrapper.BatchSize to write before yielding into P:NLog.Targets.Wrappers.AsyncTargetWrapper.TimeToSleepBetweenBatches</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeToSleepBetweenBatches" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Time in milliseconds to sleep between batches. (1 or less means trigger on new activity)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Grow" />
      <xs:enumeration value="Discard" />
      <xs:enumeration value="Block" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="AutoFlushWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="asyncFlush" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="condition" type="Condition" minOccurs="0" maxOccurs="1" />
          <xs:element name="flushOnConditionOnly" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="asyncFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Delay the flush until the LogEvent has been confirmed as written</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression. Log events who meet this condition will cause a flush on the wrapped target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="flushOnConditionOnly" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Only flush when LogEvent matches condition. Ignore explicit-flush, config-reload-flush and shutdown-flush</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BufferingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="bufferSize" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="overflowAction" type="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction" minOccurs="0" maxOccurs="1" default="Flush" />
          <xs:element name="flushTimeout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="slidingTimeout" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bufferSize" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Number of log events to be buffered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="overflowAction" type="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to take if the buffer overflows.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="flushTimeout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Timeout (in milliseconds) after which the contents of buffer will be flushed if there's no write in the specified period of time. Use -1 to disable timed flushes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="slidingTimeout" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use sliding timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Flush" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Chainsaw">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="scopeNestedSeparator" type="xs:string" minOccurs="0" maxOccurs="1" default=" " />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
          <xs:element name="ndcItemSeparator" type="xs:string" minOccurs="0" maxOccurs="1" default=" " />
          <xs:element name="loggerName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeScopeNested" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeSourceInfo" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeNdc" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="includeCallSite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="appInfo" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeNLogData" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="sslProtocols" type="System.Security.Authentication.SslProtocols" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction" minOccurs="0" maxOccurs="1" default="Split" />
          <xs:element name="maxQueueSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="10000" />
          <xs:element name="address" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="keepConnection" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="keepAliveTimeSeconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="connectionCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="5" />
          <xs:element name="maxConnections" type="xs:integer" minOccurs="0" maxOccurs="1" default="100" />
          <xs:element name="compress" type="NLog.Targets.NetworkTargetCompressionType" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="compressMinBytes" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="maxMessageSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="65000" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="lineEnding" type="LineEndingMode" minOccurs="0" maxOccurs="1" />
          <xs:element name="newLine" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="scopeNestedSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>Separator for T:NLog.ScopeContext operation-states-stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>Stack separator for log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loggerName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Renderer for log4j:event logger-xml-attribute (Default ${logger})</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext properties-dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeNested" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="appInfo" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>AppInfo field. By default it's the friendly name of the current AppDomain.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Instance of T:NLog.Layouts.Log4JXmlEventLayout that is used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNLogData" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include NLog-specific extensions to log4j schema.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more connections than P:NLog.Targets.NetworkTarget.MaxConnections.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more pending messages than P:NLog.Targets.NetworkTarget.MaxQueueSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than P:NLog.Targets.NetworkTarget.MaxMessageSize</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size for a single connection. Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive). Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum simultaneous connections. Requires P:NLog.Targets.NetworkTarget.KeepConnection = false</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compress" type="NLog.Targets.NetworkTargetCompressionType">
          <xs:annotation>
            <xs:documentation>Type of compression for protocol payload. Useful for UDP where datagram max-size is 8192 bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compressMinBytes" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Skip compression when protocol payload is below limit to reduce overhead in cpu-usage and additional headers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes. On limit breach then P:NLog.Targets.NetworkTarget.OnOverflow action is activated.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message P:NLog.Targets.NetworkTarget.NewLine.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.NetworkTargetConnectionsOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Grow" />
      <xs:enumeration value="AllowNewConnnection" />
      <xs:enumeration value="Discard" />
      <xs:enumeration value="DiscardMessage" />
      <xs:enumeration value="Block" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="System.Security.Authentication.SslProtocols">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Ssl2" />
      <xs:enumeration value="Ssl3" />
      <xs:enumeration value="Tls" />
      <xs:enumeration value="Tls11" />
      <xs:enumeration value="Tls12" />
      <xs:enumeration value="Tls13" />
      <xs:enumeration value="Default" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.NetworkTargetQueueOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Grow" />
      <xs:enumeration value="Discard" />
      <xs:enumeration value="Block" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.NetworkTargetOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Error" />
      <xs:enumeration value="Split" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.NetworkTargetCompressionType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="GZip" />
      <xs:enumeration value="GZipFast" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.NLogViewerParameterInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="includeEmptyValue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Viewer parameter name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="ColoredConsole">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="detectConsoleAvailable" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="enableAnsiOutput" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="IBM437" />
          <xs:element name="stdErr" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="autoFlush" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="detectOutputRedirected" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="useDefaultRowHighlightingRules" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="highlight-row" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.ConsoleRowHighlightingRule" />
          <xs:element name="highlight-word" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.ConsoleWordHighlightingRule" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectConsoleAvailable" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console is available. - Disables console writing if Environment.UserInteractive = False (Windows Service) - Disables console writing if Console Standard Input is not available (Non-Console-App)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableAnsiOutput" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Enables output using ANSI Color Codes</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>The encoding for writing messages to the T:System.Console.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="stdErr" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to send the log messages to the standard error instead of the standard output.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-flush after M:System.Console.WriteLine</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectOutputRedirected" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console has been redirected to file - Disables coloring logic when System.Console.IsOutputRedirected = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useDefaultRowHighlightingRules" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use default row highlighting rules.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.ConsoleOutputColor">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Black" />
      <xs:enumeration value="DarkBlue" />
      <xs:enumeration value="DarkGreen" />
      <xs:enumeration value="DarkCyan" />
      <xs:enumeration value="DarkRed" />
      <xs:enumeration value="DarkMagenta" />
      <xs:enumeration value="DarkYellow" />
      <xs:enumeration value="Gray" />
      <xs:enumeration value="DarkGray" />
      <xs:enumeration value="Blue" />
      <xs:enumeration value="Green" />
      <xs:enumeration value="Cyan" />
      <xs:enumeration value="Red" />
      <xs:enumeration value="Magenta" />
      <xs:enumeration value="Yellow" />
      <xs:enumeration value="White" />
      <xs:enumeration value="NoChange" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.ConsoleRowHighlightingRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="backgroundColor" type="NLog.Targets.ConsoleOutputColor" minOccurs="0" maxOccurs="1" default="NoChange" />
      <xs:element name="condition" type="Condition" minOccurs="0" maxOccurs="1" />
      <xs:element name="foregroundColor" type="NLog.Targets.ConsoleOutputColor" minOccurs="0" maxOccurs="1" default="NoChange" />
    </xs:choice>
    <xs:attribute name="backgroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="condition" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition that must be met in order to set the specified foreground and background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="foregroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Foreground color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.ConsoleWordHighlightingRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="backgroundColor" type="NLog.Targets.ConsoleOutputColor" minOccurs="0" maxOccurs="1" default="NoChange" />
      <xs:element name="compileRegex" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="condition" type="Condition" minOccurs="0" maxOccurs="1" />
      <xs:element name="foregroundColor" type="NLog.Targets.ConsoleOutputColor" minOccurs="0" maxOccurs="1" default="NoChange" />
      <xs:element name="ignoreCase" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="regex" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="text" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="wholeWords" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
    </xs:choice>
    <xs:attribute name="backgroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="compileRegex" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Compile the P:NLog.Targets.ConsoleWordHighlightingRule.Regex? This can improve the performance, but at the costs of more memory usage. If false, the Regex Cache is used.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="condition" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition that must be met before scanning the row for highlight of words</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="foregroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Foreground color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreCase" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to ignore case when comparing texts.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="regex" type="xs:string">
      <xs:annotation>
        <xs:documentation>Regular expression to be matched. You must specify either text or regex.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="text" type="xs:string">
      <xs:annotation>
        <xs:documentation>Text to be matched. You must specify either text or regex.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="wholeWords" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to match whole words only.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Console">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="autoFlush" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="detectConsoleAvailable" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="IBM437" />
          <xs:element name="stdErr" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="writeBuffer" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-flush after M:System.Console.WriteLine</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectConsoleAvailable" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console is available - Disables console writing if Environment.UserInteractive = False (Windows Service) - Disables console writing if Console Standard Input is not available (Non-Console-App)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>The encoding for writing messages to the T:System.Console.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="stdErr" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to send the log messages to the standard error instead of the standard output.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeBuffer" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to activate internal buffering to allow batch writing, instead of using M:System.Console.WriteLine</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Database">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="dbUserName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="dbPassword" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="dbDatabase" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="connectionStringName" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="dbHost" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="commandproperty" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseObjectPropertyInfo" />
          <xs:element name="keepConnection" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="dbProvider" type="xs:string" minOccurs="0" maxOccurs="1" default="sqlserver" />
          <xs:element name="connectionString" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="connectionproperty" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseObjectPropertyInfo" />
          <xs:element name="uninstall-command" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseCommandInfo" />
          <xs:element name="installConnectionString" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="install-command" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseCommandInfo" />
          <xs:element name="isolationLevel" type="System.Data.IsolationLevel" minOccurs="0" maxOccurs="1" />
          <xs:element name="commandText" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="commandType" type="System.Data.CommandType" minOccurs="0" maxOccurs="1" default="Text" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseParameterInfo" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbUserName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database user name. If the ConnectionString is not provided this value will be used to construct the "User ID=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbPassword" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database password. If the ConnectionString is not provided this value will be used to construct the "Password=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbDatabase" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database name. If the ConnectionString is not provided this value will be used to construct the "Database=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionStringName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the connection string (as specified in &lt;connectionStrings&gt; configuration section.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbHost" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database host name. If the ConnectionString is not provided this value will be used to construct the "Server=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep the database connection open between the log events.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbProvider" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the database provider.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionString" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Connection string. When provided, it overrides the values specified in DBHost, DBUserName, DBPassword, DBDatabase.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="installConnectionString" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Connection string using for installation and uninstallation. If not provided, regular ConnectionString is being used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="isolationLevel" type="System.Data.IsolationLevel">
          <xs:annotation>
            <xs:documentation>Configures isolated transaction batch writing. If supported by the database, then it will improve insert performance.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="commandText" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text of the SQL command to be run on each log level.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="commandType" type="System.Data.CommandType">
          <xs:annotation>
            <xs:documentation>Type of the SQL command to be run on each log level.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="System.Data.CommandType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Text" />
      <xs:enumeration value="StoredProcedure" />
      <xs:enumeration value="TableDirect" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="System.Data.IsolationLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unspecified" />
      <xs:enumeration value="Chaos" />
      <xs:enumeration value="ReadUncommitted" />
      <xs:enumeration value="ReadCommitted" />
      <xs:enumeration value="RepeatableRead" />
      <xs:enumeration value="Serializable" />
      <xs:enumeration value="Snapshot" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.DatabaseObjectPropertyInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="format" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="culture" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="propertyType" type="xs:string" minOccurs="0" maxOccurs="1" default="System.String" />
    </xs:choice>
    <xs:attribute name="format" type="xs:string">
      <xs:annotation>
        <xs:documentation>Convert format of the property value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="culture" type="xs:string">
      <xs:annotation>
        <xs:documentation>Culture used for parsing property string-value for type-conversion</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Value to assign on the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name for the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertyType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.DatabaseCommandInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="commandType" type="System.Data.CommandType" minOccurs="0" maxOccurs="1" default="Text" />
      <xs:element name="connectionString" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="ignoreFailures" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseParameterInfo" />
      <xs:element name="text" type="Layout" minOccurs="1" maxOccurs="1" />
    </xs:choice>
    <xs:attribute name="commandType" type="System.Data.CommandType">
      <xs:annotation>
        <xs:documentation>Type of the command.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="connectionString" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Connection string to run the command against. If not provided, connection string from the target is used.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreFailures" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to ignore failures.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="text" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Command text.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.DatabaseParameterInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="dbType" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="size" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
      <xs:element name="precision" type="xs:byte" minOccurs="0" maxOccurs="1" default="0" />
      <xs:element name="scale" type="xs:byte" minOccurs="0" maxOccurs="1" default="0" />
      <xs:element name="parameterType" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="defaultValue" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="format" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="culture" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="allowDbNull" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Database parameter name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="dbType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Database parameter DbType.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="size" type="xs:integer">
      <xs:annotation>
        <xs:documentation>Database parameter size.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="precision" type="xs:byte">
      <xs:annotation>
        <xs:documentation>Database parameter precision.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="scale" type="xs:byte">
      <xs:annotation>
        <xs:documentation>Database parameter scale.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parameterType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="defaultValue" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Fallback value when result value is not available</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="format" type="xs:string">
      <xs:annotation>
        <xs:documentation>Convert format of the database parameter value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="culture" type="xs:string">
      <xs:annotation>
        <xs:documentation>Culture used for parsing parameter string-value for type-conversion</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="allowDbNull" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether empty value should translate into DbNull. Requires database column to allow NULL values.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Debugger">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="DebugSystem">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Debug">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EventLog">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="category" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="entryType" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="eventId" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="log" type="xs:string" minOccurs="0" maxOccurs="1" default="Application" />
          <xs:element name="machineName" type="xs:string" minOccurs="0" maxOccurs="1" default="." />
          <xs:element name="maxKilobytes" type="xs:long" minOccurs="0" maxOccurs="1" />
          <xs:element name="maxMessageLength" type="xs:integer" minOccurs="0" maxOccurs="1" default="30000" />
          <xs:element name="source" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="onOverflow" type="NLog.Targets.EventLogTargetOverflowAction" minOccurs="0" maxOccurs="1" default="Truncate" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="category" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout that renders event Category.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="entryType" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Optional entry type. When not set, or when not convertible to T:System.Diagnostics.EventLogEntryType then determined by T:NLog.LogLevel</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="eventId" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout that renders event ID.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="log" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the Event Log to write to. This can be System, Application or any user-defined name.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="machineName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the machine on which Event Log service is running.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxKilobytes" type="xs:long">
          <xs:annotation>
            <xs:documentation>Maximum Event log size in kilobytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Message length limit to write to the Event Log.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="source" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Value to be used as the event Source.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.EventLogTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to take if the message is larger than the P:NLog.Targets.EventLogTarget.MaxMessageLength option.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.EventLogTargetOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Truncate" />
      <xs:enumeration value="Split" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FallbackGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="returnToFirstOnSuccess" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="enableBatchWrite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="returnToFirstOnSuccess" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to return to the first target after any successful write.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableBatchWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to enable batching, but fallback will be handled individually</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="File">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="fileName" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="maxArchiveDays" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="enableArchiveFileCompression" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="archiveOldFileOnStartupAboveSize" type="xs:long" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="archiveOldFileOnStartup" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="archiveFileKind" type="NLog.Targets.FilePathKind" minOccurs="0" maxOccurs="1" default="Unknown" />
          <xs:element name="archiveFileName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="maxArchiveFiles" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="archiveEvery" type="NLog.Targets.FileArchivePeriod" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="archiveDateFormat" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="archiveAboveSize" type="xs:long" minOccurs="0" maxOccurs="1" default="-1" />
          <xs:element name="archiveNumbering" type="NLog.Targets.ArchiveNumberingMode" minOccurs="0" maxOccurs="1" default="Sequence" />
          <xs:element name="writeFooterOnArchivingOnly" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="writeHeaderWhenInitialFileNotEmpty" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="createDirs" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="replaceFileContentsOnEachWrite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="lineEnding" type="LineEndingMode" minOccurs="0" maxOccurs="1" />
          <xs:element name="forceMutexConcurrentWrites" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="forceManaged" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="fileNameKind" type="NLog.Targets.FilePathKind" minOccurs="0" maxOccurs="1" default="Unknown" />
          <xs:element name="fileAttributes" type="NLog.Targets.Win32FileAttributes" minOccurs="0" maxOccurs="1" default="Normal" />
          <xs:element name="cleanupFileName" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="writeBom" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="enableFileDelete" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="deleteOldFileOnStartup" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="keepFileOpen" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="discardAll" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="concurrentWriteAttempts" type="xs:integer" minOccurs="0" maxOccurs="1" default="10" />
          <xs:element name="concurrentWriteAttemptDelay" type="xs:integer" minOccurs="0" maxOccurs="1" default="1" />
          <xs:element name="openFileFlushTimeout" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="openFileCacheTimeout" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="networkWrites" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="bufferSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="32768" />
          <xs:element name="autoFlush" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="concurrentWrites" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="openFileCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="5" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Name of the file to write to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxArchiveDays" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum days of archive files that should be kept.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableArchiveFileCompression" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to compress archive files into the zip archive format.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveOldFileOnStartupAboveSize" type="xs:long">
          <xs:annotation>
            <xs:documentation>Value of the file size threshold to archive old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveOldFileOnStartup" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to archive old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveFileKind" type="NLog.Targets.FilePathKind">
          <xs:annotation>
            <xs:documentation>Is the P:NLog.Targets.FileTarget.ArchiveFileName an absolute or relative path?</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveFileName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Name of the file to be used for an archive.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxArchiveFiles" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of archive files that should be kept.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveEvery" type="NLog.Targets.FileArchivePeriod">
          <xs:annotation>
            <xs:documentation>Indicates whether to automatically archive log files every time the specified time passes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveDateFormat" type="xs:string">
          <xs:annotation>
            <xs:documentation>Value specifying the date format to use when archiving files.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveAboveSize" type="xs:long">
          <xs:annotation>
            <xs:documentation>Size in bytes above which log files will be automatically archived.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveNumbering" type="NLog.Targets.ArchiveNumberingMode">
          <xs:annotation>
            <xs:documentation>Way file archives are numbered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeFooterOnArchivingOnly" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether the footer should be written only when the file is archived.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeHeaderWhenInitialFileNotEmpty" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to write the Header on initial creation of file appender, even if the file is not empty. Default value is false, which means only write header when initial file is empty (Ex. ensures valid CSV files)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="createDirs" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to create directories if they do not exist.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="replaceFileContentsOnEachWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to replace file contents on each write instead of appending log message at the end.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>Line ending mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceMutexConcurrentWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether file creation calls should be synchronized by a system global mutex.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceManaged" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Gets or set a value indicating whether a managed file stream is forced, instead of using the native implementation.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileNameKind" type="NLog.Targets.FilePathKind">
          <xs:annotation>
            <xs:documentation>Is the P:NLog.Targets.FileTarget.FileName an absolute or relative path?</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileAttributes" type="NLog.Targets.Win32FileAttributes">
          <xs:annotation>
            <xs:documentation>File attributes (Windows only).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="cleanupFileName" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Cleanup invalid values in a filename, e.g. slashes in a filename. If set to true, this can impact the performance of massive writes. If set to false, nothing gets written when the filename is wrong.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeBom" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to write BOM (byte order mark) in created files. Defaults to true for UTF-16 and UTF-32</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableFileDelete" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to enable log file(s) to be deleted.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>File encoding.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="deleteOldFileOnStartup" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to delete old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepFileOpen" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep log file open instead of opening and closing it on each logging event.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="discardAll" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether or not this target should just discard all data that its asked to write. Mostly used for when testing NLog Stack except final write</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWriteAttempts" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of times the write is appended on the file before NLog discards the log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWriteAttemptDelay" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Delay in milliseconds to wait before attempting to write to the file again.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileFlushTimeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of seconds before open files are flushed. Zero or negative means disabled.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileCacheTimeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of seconds that files are kept open. Zero or negative means disabled.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="networkWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether concurrent writes to the log file by multiple processes on different network hosts.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bufferSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Log file buffer size in bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to automatically flush the file buffers after each log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether concurrent writes to the log file by multiple processes on the same host.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of files to be kept open. Setting this to a higher value may improve performance in a situation where a single File target is writing to many files (such as splitting by level or by logger).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.FilePathKind">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unknown" />
      <xs:enumeration value="Relative" />
      <xs:enumeration value="Absolute" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.FileArchivePeriod">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Year" />
      <xs:enumeration value="Month" />
      <xs:enumeration value="Day" />
      <xs:enumeration value="Hour" />
      <xs:enumeration value="Minute" />
      <xs:enumeration value="Sunday" />
      <xs:enumeration value="Monday" />
      <xs:enumeration value="Tuesday" />
      <xs:enumeration value="Wednesday" />
      <xs:enumeration value="Thursday" />
      <xs:enumeration value="Friday" />
      <xs:enumeration value="Saturday" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.ArchiveNumberingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Sequence" />
      <xs:enumeration value="Rolling" />
      <xs:enumeration value="Date" />
      <xs:enumeration value="DateAndSequence" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.Win32FileAttributes">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ReadOnly" />
      <xs:enumeration value="Hidden" />
      <xs:enumeration value="System" />
      <xs:enumeration value="Archive" />
      <xs:enumeration value="Device" />
      <xs:enumeration value="Normal" />
      <xs:enumeration value="Temporary" />
      <xs:enumeration value="SparseFile" />
      <xs:enumeration value="ReparsePoint" />
      <xs:enumeration value="Compressed" />
      <xs:enumeration value="NotContentIndexed" />
      <xs:enumeration value="Encrypted" />
      <xs:enumeration value="WriteThrough" />
      <xs:enumeration value="NoBuffering" />
      <xs:enumeration value="DeleteOnClose" />
      <xs:enumeration value="PosixSemantics" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FilteringWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="condition" type="Condition" minOccurs="0" maxOccurs="1" />
          <xs:element name="filter" type="Filter" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression. Log events who meet this condition will be forwarded to the wrapped target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GroupByWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="key" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="key" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Identifier to perform group-by</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="LimitingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="interval" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="messageLimit" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="interval" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Interval in which messages will be written up to the P:NLog.Targets.Wrappers.LimitingTargetWrapper.MessageLimit number of messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="messageLimit" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Maximum allowed number of messages written per P:NLog.Targets.Wrappers.LimitingTargetWrapper.Interval.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Mail">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="replaceNewlineWithBrTagInHtml" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="priority" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="mailheader" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="bcc" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="addNewLines" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="to" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="html" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="from" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="body" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="subject" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="cc" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="deliveryMethod" type="System.Net.Mail.SmtpDeliveryMethod" minOccurs="0" maxOccurs="1" default="Network" />
          <xs:element name="smtpServer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="smtpAuthentication" type="NLog.Targets.SmtpAuthenticationMode" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="smtpUserName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="smtpPassword" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="enableSsl" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="smtpPort" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="useSystemNetMailSettings" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="pickupDirectoryLocation" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="timeout" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="replaceNewlineWithBrTagInHtml" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether NewLine characters in the body should be replaced with  tags.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="priority" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Priority used for sending mails.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used for sending e-mail.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bcc" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>BCC email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="addNewLines" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to add new lines between log entries.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="to" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Recipients' email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="html" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to send message as HTML instead of plain text.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="from" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Sender's email address (e.g. <EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="body" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Mail message body (repeated for each log message send in one mail).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="subject" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Mail subject.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="cc" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>CC email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="deliveryMethod" type="System.Net.Mail.SmtpDeliveryMethod">
          <xs:annotation>
            <xs:documentation>Specifies how outgoing email messages will be handled.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpServer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>SMTP Server to be used for sending.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpAuthentication" type="NLog.Targets.SmtpAuthenticationMode">
          <xs:annotation>
            <xs:documentation>SMTP Authentication mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpUserName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Username used to connect to SMTP server (used when SmtpAuthentication is set to "basic").</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpPassword" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Password used to authenticate against SMTP server (used when SmtpAuthentication is set to "basic").</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableSsl" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether SSL (secure sockets layer) should be used when communicating with SMTP server.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpPort" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Port number that SMTP Server is listening on.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useSystemNetMailSettings" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether the default Settings from System.Net.MailSettings should be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="pickupDirectoryLocation" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Folder where applications save mail messages to be processed by the local SMTP server.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Indicates the SMTP client timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="System.Net.Mail.SmtpDeliveryMethod">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Network" />
      <xs:enumeration value="SpecifiedPickupDirectory" />
      <xs:enumeration value="PickupDirectoryFromIis" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.SmtpAuthenticationMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Basic" />
      <xs:enumeration value="Ntlm" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.MethodCallParameter">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="defaultValue" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="parameterType" type="xs:string" minOccurs="0" maxOccurs="1" default="System.String" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="defaultValue" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Fallback value when result value is not available</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parameterType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Memory">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="maxLogsCount" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxLogsCount" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max number of items to have in memory</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MethodCall">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="className" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="methodName" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="className" type="xs:string">
          <xs:annotation>
            <xs:documentation>Class name.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="methodName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Method name. The method must be public and static. Use the AssemblyQualifiedName , https://msdn.microsoft.com/en-us/library/system.type.assemblyqualifiedname(v=vs.110).aspx e.g.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Network">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="sslProtocols" type="System.Security.Authentication.SslProtocols" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction" minOccurs="0" maxOccurs="1" default="Split" />
          <xs:element name="maxQueueSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="10000" />
          <xs:element name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="keepConnection" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="keepAliveTimeSeconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="connectionCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="5" />
          <xs:element name="address" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="maxConnections" type="xs:integer" minOccurs="0" maxOccurs="1" default="100" />
          <xs:element name="compress" type="NLog.Targets.NetworkTargetCompressionType" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="compressMinBytes" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="maxMessageSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="65000" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="lineEnding" type="LineEndingMode" minOccurs="0" maxOccurs="1" />
          <xs:element name="newLine" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more pending messages than P:NLog.Targets.NetworkTarget.MaxQueueSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than P:NLog.Targets.NetworkTarget.MaxMessageSize</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size for a single connection. Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more connections than P:NLog.Targets.NetworkTarget.MaxConnections.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive). Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum simultaneous connections. Requires P:NLog.Targets.NetworkTarget.KeepConnection = false</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compress" type="NLog.Targets.NetworkTargetCompressionType">
          <xs:annotation>
            <xs:documentation>Type of compression for protocol payload. Useful for UDP where datagram max-size is 8192 bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compressMinBytes" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Skip compression when protocol payload is below limit to reduce overhead in cpu-usage and additional headers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes. On limit breach then P:NLog.Targets.NetworkTarget.OnOverflow action is activated.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message P:NLog.Targets.NetworkTarget.NewLine.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLogViewer">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="scopeNestedSeparator" type="xs:string" minOccurs="0" maxOccurs="1" default=" " />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
          <xs:element name="ndcItemSeparator" type="xs:string" minOccurs="0" maxOccurs="1" default=" " />
          <xs:element name="loggerName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeScopeNested" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeSourceInfo" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeNdc" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeCallSite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="appInfo" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeNLogData" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="sslProtocols" type="System.Security.Authentication.SslProtocols" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction" minOccurs="0" maxOccurs="1" default="Discard" />
          <xs:element name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction" minOccurs="0" maxOccurs="1" default="Split" />
          <xs:element name="maxQueueSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="10000" />
          <xs:element name="address" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="keepConnection" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="keepAliveTimeSeconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="connectionCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="5" />
          <xs:element name="maxConnections" type="xs:integer" minOccurs="0" maxOccurs="1" default="100" />
          <xs:element name="compress" type="NLog.Targets.NetworkTargetCompressionType" minOccurs="0" maxOccurs="1" default="None" />
          <xs:element name="compressMinBytes" type="xs:integer" minOccurs="0" maxOccurs="1" default="0" />
          <xs:element name="maxMessageSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="65000" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="lineEnding" type="LineEndingMode" minOccurs="0" maxOccurs="1" />
          <xs:element name="newLine" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="scopeNestedSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>Separator for T:NLog.ScopeContext operation-states-stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>Stack separator for log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loggerName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Renderer for log4j:event logger-xml-attribute (Default ${logger})</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext properties-dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeNested" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="appInfo" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>AppInfo field. By default it's the friendly name of the current AppDomain.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Instance of T:NLog.Layouts.Log4JXmlEventLayout that is used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNLogData" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include NLog-specific extensions to log4j schema.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more connections than P:NLog.Targets.NetworkTarget.MaxConnections.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onQueueOverflow" type="NLog.Targets.NetworkTargetQueueOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken, when more pending messages than P:NLog.Targets.NetworkTarget.MaxQueueSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than P:NLog.Targets.NetworkTarget.MaxMessageSize</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size for a single connection. Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive). Requires P:NLog.Targets.NetworkTarget.KeepConnection = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum simultaneous connections. Requires P:NLog.Targets.NetworkTarget.KeepConnection = false</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compress" type="NLog.Targets.NetworkTargetCompressionType">
          <xs:annotation>
            <xs:documentation>Type of compression for protocol payload. Useful for UDP where datagram max-size is 8192 bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compressMinBytes" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Skip compression when protocol payload is below limit to reduce overhead in cpu-usage and additional headers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes. On limit breach then P:NLog.Targets.NetworkTarget.OnOverflow action is activated.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message P:NLog.Targets.NetworkTarget.NewLine.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Null">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="formatMessage" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="formatMessage" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to perform layout calculation.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PostFilteringWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="defaultFilter" type="Condition" minOccurs="0" maxOccurs="1" />
          <xs:element name="when" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.Wrappers.FilteringRule" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="defaultFilter" type="Condition">
          <xs:annotation>
            <xs:documentation>Default filter to be applied when no specific rule matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.Wrappers.FilteringRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="exists" type="Condition" minOccurs="0" maxOccurs="1" />
      <xs:element name="filter" type="Condition" minOccurs="0" maxOccurs="1" />
    </xs:choice>
    <xs:attribute name="exists" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition to be tested.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="filter" type="Condition">
      <xs:annotation>
        <xs:documentation>Resulting filter to be applied when the condition matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="RandomizeGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RepeatingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="repeatCount" type="xs:integer" minOccurs="0" maxOccurs="1" default="3" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="repeatCount" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of times to repeat each log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RetryingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="enableBatchWrite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="retryCount" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="retryDelayMilliseconds" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableBatchWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to enable batching, and only apply single delay when a whole batch fails</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="retryCount" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Number of retries that should be attempted on the wrapped target in case of a failure.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="retryDelayMilliseconds" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Time to wait between retries in milliseconds.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RoundRobinGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SplitGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Trace">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="enableTraceFail" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="rawWrite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableTraceFail" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Forward F:NLog.LogLevel.Fatal to M:System.Diagnostics.Trace.Fail(System.String) (Instead of M:System.Diagnostics.Trace.TraceError(System.String))</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="rawWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Force use M:System.Diagnostics.Trace.WriteLine(System.String) independent of T:NLog.LogLevel</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="WebService">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="preAuthenticate" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="escapeDataRfc3986" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="escapeDataNLogLegacy" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="userAgent" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="url" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="proxyType" type="NLog.Targets.WebServiceProxyType" minOccurs="0" maxOccurs="1" default="DefaultWebProxy" />
          <xs:element name="proxyAddress" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="protocol" type="NLog.Targets.WebServiceProtocol" minOccurs="0" maxOccurs="1" default="Soap11" />
          <xs:element name="namespace" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="methodName" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeBOM" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="header" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="encoding" type="xs:string" minOccurs="0" maxOccurs="1" default="utf-8" />
          <xs:element name="xmlRoot" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="xmlRootNamespace" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="preAuthenticate" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to pre-authenticate the HttpWebRequest (Requires 'Authorization' in P:NLog.Targets.WebServiceTarget.Headers parameters)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="escapeDataRfc3986" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Value whether escaping be done according to Rfc3986 (Supports Internationalized Resource Identifiers - IRIs)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="escapeDataNLogLegacy" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Value whether escaping be done according to the old NLog style (Very non-standard)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="userAgent" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Value of the User-agent HTTP header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="url" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Web service URL.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="proxyType" type="NLog.Targets.WebServiceProxyType">
          <xs:annotation>
            <xs:documentation>Proxy configuration when calling web service</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="proxyAddress" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Custom proxy address, include port separated by a colon</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="protocol" type="NLog.Targets.WebServiceProtocol">
          <xs:annotation>
            <xs:documentation>Protocol to be used when calling web service.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="namespace" type="xs:string">
          <xs:annotation>
            <xs:documentation>Web service namespace. Only used with Soap.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="methodName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Web service method name. Only used with Soap.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeBOM" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Should we include the BOM (Byte-order-mark) for UTF? Influences the P:NLog.Targets.WebServiceTarget.Encoding property. This will only work for UTF-8.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="xmlRoot" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the root XML element, if POST of XML document chosen. If so, this property must not be null. (see P:NLog.Targets.WebServiceTarget.Protocol and F:NLog.Targets.WebServiceProtocol.XmlPost).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="xmlRootNamespace" type="xs:string">
          <xs:annotation>
            <xs:documentation>(optional) root namespace of the XML document, if POST of XML document chosen. (see P:NLog.Targets.WebServiceTarget.Protocol and F:NLog.Targets.WebServiceProtocol.XmlPost).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.WebServiceProxyType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DefaultWebProxy" />
      <xs:enumeration value="AutoProxy" />
      <xs:enumeration value="NoProxy" />
      <xs:enumeration value="ProxyAddress" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.WebServiceProtocol">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Soap11" />
      <xs:enumeration value="Soap12" />
      <xs:enumeration value="HttpPost" />
      <xs:enumeration value="HttpGet" />
      <xs:enumeration value="JsonPost" />
      <xs:enumeration value="XmlPost" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="CompoundLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="layout" minOccurs="0" maxOccurs="unbounded" type="Layout" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Layout">
    <xs:choice minOccurs="0" maxOccurs="unbounded" />
  </xs:complexType>
  <xs:complexType name="CsvLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="column" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.CsvColumn" />
          <xs:element name="customColumnDelimiter" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="delimiter" type="NLog.Layouts.CsvColumnDelimiterMode" minOccurs="0" maxOccurs="1" default="Auto" />
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="quoteChar" type="xs:string" minOccurs="0" maxOccurs="1" default="&quot;" />
          <xs:element name="quoting" type="NLog.Layouts.CsvQuotingMode" minOccurs="0" maxOccurs="1" default="Auto" />
          <xs:element name="withHeader" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
        </xs:choice>
        <xs:attribute name="customColumnDelimiter" type="xs:string">
          <xs:annotation>
            <xs:documentation>Custom column delimiter value (valid when P:NLog.Layouts.CsvLayout.Delimiter is set to F:NLog.Layouts.CsvColumnDelimiterMode.Custom).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="delimiter" type="NLog.Layouts.CsvColumnDelimiterMode">
          <xs:annotation>
            <xs:documentation>Column delimiter.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Body layout (can be repeated multiple times).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="quoteChar" type="xs:string">
          <xs:annotation>
            <xs:documentation>Quote Character.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="quoting" type="NLog.Layouts.CsvQuotingMode">
          <xs:annotation>
            <xs:documentation>Quoting mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="withHeader" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether CVS should include header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Layouts.CsvQuotingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="All" />
      <xs:enumeration value="Nothing" />
      <xs:enumeration value="Auto" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Layouts.CsvColumnDelimiterMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="Comma" />
      <xs:enumeration value="Semicolon" />
      <xs:enumeration value="Tab" />
      <xs:enumeration value="Pipe" />
      <xs:enumeration value="Space" />
      <xs:enumeration value="Custom" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Layouts.CsvColumn">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="quoting" type="NLog.Layouts.CsvQuotingMode" minOccurs="0" maxOccurs="1" default="Auto" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the column.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout of the column.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="quoting" type="NLog.Layouts.CsvQuotingMode">
      <xs:annotation>
        <xs:documentation>Override of Quoting mode</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="JsonArrayLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="item" minOccurs="0" maxOccurs="unbounded" type="Layout" />
          <xs:element name="renderEmptyObject" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="suppressSpaces" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="renderEmptyObject" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to render the empty object value {}</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="suppressSpaces" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to suppress the extra spaces in the output json</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="JsonLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.JsonAttribute" />
          <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeGdc" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="escapeForwardSlash" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="excludeEmptyProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="excludeProperties" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="indentJson" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="maxRecursionLimit" type="xs:integer" minOccurs="0" maxOccurs="1" default="1" />
          <xs:element name="renderEmptyObject" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="suppressSpaces" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log event (as JSON)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeGdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the T:NLog.GlobalDiagnosticsContext dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="escapeForwardSlash" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Should forward slashes be escaped? If true, / will be converted to \/</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="excludeEmptyProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to exclude null/empty properties from the log event (as JSON)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="excludeProperties" type="xs:string">
          <xs:annotation>
            <xs:documentation>List of property names to exclude when P:NLog.Layouts.JsonLayout.IncludeAllProperties is true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="indentJson" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Auto indent and create new lines</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxRecursionLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How far should the JSON serializer follow object references before backing off</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="renderEmptyObject" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to render the empty object value {}</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="suppressSpaces" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to suppress the extra spaces in the output json</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.JsonAttribute">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="defaultValue" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="encode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
      <xs:element name="escapeForwardSlash" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="escapeUnicode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="includeEmptyValue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="valueType" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the attribute.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that will be rendered as the attribute's value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="defaultValue" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Fallback value when result value is not available</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether output should be encoded as Json-String-Property, or be treated as valid json.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="escapeForwardSlash" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Should forward slashes be escaped? If true, / will be converted to \/</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="escapeUnicode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to escape non-ascii characters</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="valueType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Result value type, for conversion of layout rendering output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="LayoutWithHeaderAndFooter">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="footer" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="header" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Body layout (can be repeated multiple times).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Log4JXmlEventLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="includeNdc" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeScopeNested" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
          <xs:element name="appInfo" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeCallSite" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="includeSourceInfo" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="loggerName" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="writeThrowableCData" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeNested" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include log4j:NDC in output from T:NLog.ScopeContext nested context.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext properties-dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="appInfo" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>AppInfo field. By default it's the friendly name of the current AppDomain.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loggerName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Log4j:event logger-xml-attribute (Default ${logger})</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeThrowableCData" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether the log4j:throwable xml-element should be written as CDATA</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SimpleLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="text" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="text" type="xs:string">
          <xs:annotation>
            <xs:documentation>Layout text.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="XmlLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlAttribute" />
          <xs:element name="elementName" type="xs:string" minOccurs="0" maxOccurs="1" default="logevent" />
          <xs:element name="element" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlElement" />
          <xs:element name="elementValue" type="Layout" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="elementEncode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
          <xs:element name="excludeProperties" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeEmptyValue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="indentXml" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="maxRecursionLimit" type="xs:integer" minOccurs="0" maxOccurs="1" default="1" />
          <xs:element name="propertiesCollectionItemName" type="xs:string" minOccurs="0" maxOccurs="1" default="item" />
          <xs:element name="propertiesElementKeyAttribute" type="xs:string" minOccurs="0" maxOccurs="1" default="key" />
          <xs:element name="propertiesElementName" type="xs:string" minOccurs="0" maxOccurs="1" default="property" />
          <xs:element name="propertiesElementValueAttribute" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
        </xs:choice>
        <xs:attribute name="elementName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the root XML element</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="elementValue" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Value inside the root XML element</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeScopeProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="elementEncode" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Determines whether or not this attribute will be Xml encoded.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="excludeProperties" type="xs:string">
          <xs:annotation>
            <xs:documentation>List of property names to exclude when P:NLog.Layouts.XmlElementBase.IncludeAllProperties is true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEmptyValue" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether a ElementValue with empty value should be included in the output</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="indentXml" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Auto indent and create new lines</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxRecursionLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How far should the XML serializer follow object references before backing off</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesCollectionItemName" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML element name to use for rendering IList-collections items</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementKeyAttribute" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML attribute name to use when rendering property-key When null (or empty) then key-attribute is not included</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementName" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML element name to use when rendering properties</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementValueAttribute" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML attribute name to use when rendering property-value When null (or empty) then value-attribute is not included and value is formatted as XML-element-value</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log event (as XML)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.XmlAttribute">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
      <xs:element name="defaultValue" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="encode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
      <xs:element name="includeEmptyValue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="valueType" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the attribute.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that will be rendered as the attribute's value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="defaultValue" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Fallback value when result value is not available</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether output should be encoded with Xml-string escaping, or be treated as valid xml-attribute-value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="valueType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Result value type, for conversion of layout rendering output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.XmlElement">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" default="item" />
      <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlAttribute" />
      <xs:element name="element" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlElement" />
      <xs:element name="includeScopeProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="layout" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="value" type="Layout" minOccurs="0" maxOccurs="1" />
      <xs:element name="encode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true" />
      <xs:element name="excludeProperties" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="includeEmptyValue" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="indentXml" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
      <xs:element name="maxRecursionLimit" type="xs:integer" minOccurs="0" maxOccurs="1" default="1" />
      <xs:element name="propertiesCollectionItemName" type="xs:string" minOccurs="0" maxOccurs="1" default="item" />
      <xs:element name="propertiesElementKeyAttribute" type="xs:string" minOccurs="0" maxOccurs="1" default="key" />
      <xs:element name="propertiesElementName" type="xs:string" minOccurs="0" maxOccurs="1" default="property" />
      <xs:element name="propertiesElementValueAttribute" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="includeEventProperties" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the element</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeScopeProperties" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether to include the contents of the T:NLog.ScopeContext dictionary.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Value inside the element</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="value" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Value inside the element</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether output should be encoded with Xml-string escaping, or be treated as valid xml-element-value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="excludeProperties" type="xs:string">
      <xs:annotation>
        <xs:documentation>List of property names to exclude when P:NLog.Layouts.XmlElementBase.IncludeAllProperties is true</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether a ElementValue with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="indentXml" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Auto indent and create new lines</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="maxRecursionLimit" type="xs:integer">
      <xs:annotation>
        <xs:documentation>How far should the XML serializer follow object references before backing off</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesCollectionItemName" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML element name to use for rendering IList-collections items</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementKeyAttribute" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML attribute name to use when rendering property-key When null (or empty) then key-attribute is not included</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementName" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML element name to use when rendering properties</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementValueAttribute" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML attribute name to use when rendering property-value When null (or empty) then value-attribute is not included and value is formatted as XML-element-value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEventProperties" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Option to include all properties from the log event (as XML)</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="when">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="condition" type="Condition" minOccurs="0" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="FilterResult">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Neutral" />
      <xs:enumeration value="Log" />
      <xs:enumeration value="Ignore" />
      <xs:enumeration value="LogFinal" />
      <xs:enumeration value="IgnoreFinal" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="whenContains">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="ignoreCase" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="substring" type="xs:string" minOccurs="1" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="substring" type="xs:string">
          <xs:annotation>
            <xs:documentation>Substring to be matched.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenEqual">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="compareTo" type="xs:string" minOccurs="1" maxOccurs="1" />
          <xs:element name="ignoreCase" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compareTo" type="xs:string">
          <xs:annotation>
            <xs:documentation>String to compare the layout to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenNotContains">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="ignoreCase" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="substring" type="xs:string" minOccurs="1" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="substring" type="xs:string">
          <xs:annotation>
            <xs:documentation>Substring to be matched.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenNotEqual">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="compareTo" type="xs:string" minOccurs="1" maxOccurs="1" />
          <xs:element name="ignoreCase" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compareTo" type="xs:string">
          <xs:annotation>
            <xs:documentation>String to compare the layout to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenRepeated">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" type="FilterResult" minOccurs="0" maxOccurs="1" default="Neutral" />
          <xs:element name="filterCountMessageAppendFormat" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="filterCountPropertyName" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="includeFirst" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false" />
          <xs:element name="layout" type="Layout" minOccurs="1" maxOccurs="1" />
          <xs:element name="maxLength" type="xs:integer" minOccurs="0" maxOccurs="1" default="1000" />
          <xs:element name="timeoutSeconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="10" />
          <xs:element name="defaultFilterCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="1000" />
          <xs:element name="maxFilterCacheSize" type="xs:integer" minOccurs="0" maxOccurs="1" default="50000" />
          <xs:element name="optimizeBufferDefaultLength" type="xs:integer" minOccurs="0" maxOccurs="1" default="1000" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="filterCountMessageAppendFormat" type="xs:string">
          <xs:annotation>
            <xs:documentation>Append FilterCount to the P:NLog.LogEventInfo.Message when an event is no longer filtered</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="filterCountPropertyName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Insert FilterCount value into P:NLog.LogEventInfo.Properties when an event is no longer filtered</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeFirst" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Applies the configured action to the initial logevent that starts the timeout period. Used to configure that it should ignore all events until timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max length of filter values, will truncate if above limit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeoutSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How long before a filter expires, and logging is accepted again</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="defaultFilterCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Default number of unique filter values to expect, will automatically increase if needed</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxFilterCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max number of unique filter values to expect simultaneously</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferDefaultLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Default buffer size for the internal buffers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AccurateLocal">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AccurateUTC">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="FastLocal">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="FastUTC">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>