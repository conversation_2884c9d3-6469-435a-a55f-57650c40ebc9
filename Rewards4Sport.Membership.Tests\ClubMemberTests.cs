﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using Rewards4Sport.Membership.Api.Data;
using Rewards4Sport.Membership.Api.Models;
using Rewards4Sport.Membership.Api.Services;

namespace Rewards4Sport.Membership.Tests
{
    [TestFixture]
    public class ClubMemberTests
    {
        private Mock<ClubsEntities> _clubsEntities;

        [SetUp]
        public void Setup()
        {
            _clubsEntities = new Mock<ClubsEntities>();

            _clubsEntities.SetupProperty(m => m.ClubMembers);
        }

        [Test]
        public void GivenNonClubSite_ThenItReturnsFalse()
        {
            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 0,
                NewClubId = 0
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.False(result);
            _clubsEntities.Verify(m => m.Save<PERSON>hanges(), Times.Never);
        }

        [Test]
        public void GivenNewClubSite_ThenItReturnsFalse()
        {
            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 1100,
                NewClubId = 1100
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.False(result);
            _clubsEntities.Verify(m => m.SaveChanges(), Times.Never);
        }

        [Test]
        public void GivenMemberWithNoClubRecord_ThenItUpdatesClubId()
        {
            _clubsEntities.Object.ClubMembers = new FakeDbSet<ClubMember>();

            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 8,
                NewClubId = 1015
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.False(result);
            Assert.AreEqual(_clubsEntities.Object.ClubMembers.Single().clubID, 1015);
            _clubsEntities.Verify(m => m.SaveChanges(), Times.Once);
        }

        [Test]
        public void GivenMemberIsOnClubSiteAlready_ThenItReturnsFalse()
        {
            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 1100,
                NewClubId = 1100
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.False(result);
            _clubsEntities.Verify(m => m.SaveChanges(), Times.Never);
        }

        [Test]
        public void GivenMemberOnOldClubSite_ThenItUpdatesClubId()
        {
            _clubsEntities.Object.ClubMembers = new FakeDbSet<ClubMember>()
            {
                new ClubMember()
                {
                    memberID = 1,
                    clubID = 8
                }
            };

            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 8,
                NewClubId = 1015
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.False(result);
            Assert.AreEqual(_clubsEntities.Object.ClubMembers.Single().clubID, 1015);
            _clubsEntities.Verify(m => m.SaveChanges(), Times.Once);
        }

        [Test]
        public void GivenMemberTryingToLoginToWrongSite_ThenItReturnsTrue()
        {
            _clubsEntities.Object.ClubMembers = new FakeDbSet<ClubMember>()
            {
                new ClubMember()
                {
                    memberID = 1,
                    clubID = 7,
                }
            };

            var memberService = new MembershipService(null, null, _clubsEntities.Object, null);

            var memberId = 1;

            var loginModel = new LoginModel()
            {
                Application = "rewards4sport",
                ClubId = 10,
                NewClubId = 1015
            };

            var result = memberService.CheckIfClubIdIsCorrect(loginModel, memberId);

            Assert.True(result);
            _clubsEntities.Verify(m => m.SaveChanges(), Times.Never);
        }
    }
}
