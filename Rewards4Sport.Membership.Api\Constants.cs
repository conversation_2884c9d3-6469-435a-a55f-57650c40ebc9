﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Rewards4Sport.Membership.Api
{
    public class Constants
    {
        public const string PasswordComplexity = "^(?=.*?[a-z])(?=.*?[0-9]).{8,}$";

        public static DateTime DefaultDate => new DateTime(1900, 1, 1);

        public static List<string> ApplicationList =
            new List<string>()
            {
                "rewards4cricket",
                "rewards4golf",
                "rewards4football",
                "rewards4racing",
                "rewards4rugby",
                "rewards4sport",
                "rewards4rugbyleague"
            };
    }
}