﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net48" />
  <package id="Castle.Core" version="5.1.1" targetFramework="net48" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net48" />
  <package id="Moq" version="4.20.70" targetFramework="net48" />
  <package id="MvcIntegrationTestFramework" version="1.0.8" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NUnit" version="3.14.0" targetFramework="net48" />
  <package id="NUnit3TestAdapter" version="4.5.0" targetFramework="net48" developmentDependency="true" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
</packages>