﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="CommonServiceLocator" version="2.0.7" targetFramework="net48" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net48" />
  <package id="Exceptionless" version="6.0.3" targetFramework="net48" />
  <package id="Exceptionless.Mvc" version="6.0.3" targetFramework="net48" />
  <package id="FluentValidation" version="11.9.0" targetFramework="net48" />
  <package id="Hangfire" version="1.8.7" targetFramework="net48" />
  <package id="Hangfire.Core" version="1.8.7" targetFramework="net48" />
  <package id="Hangfire.Pro.Redis" version="3.0.4" targetFramework="net48" />
  <package id="Hangfire.SqlServer" version="1.8.7" targetFramework="net48" />
  <package id="Hangfire.StructureMap3" version="1.5.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="4.1.0" targetFramework="net48" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net48" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.2" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net48" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NLog" version="5.2.8" targetFramework="net48" />
  <package id="NLog.Schema" version="5.2.8" targetFramework="net48" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="RestSharp" version="110.2.0" targetFramework="net48" />
  <package id="StructureMap" version="4.7.1" targetFramework="net48" />
  <package id="StructureMap.MVC5" version="*********" targetFramework="net472" />
  <package id="structuremap.web" version="4.0.0.315" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="8.0.0" targetFramework="net48" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Reflection.Emit.Lightweight" version="4.7.0" targetFramework="net48" />
  <package id="System.Reflection.Metadata" version="8.0.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WebActivatorEx" version="2.2.0" targetFramework="net48" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
</packages>