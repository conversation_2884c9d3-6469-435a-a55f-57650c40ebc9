//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Rewards4Sport.Membership.Api.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Member
    {
        public int memberID { get; set; }
        public System.Guid UserID { get; set; }
        public int membertypeID { get; set; }
        public int titleID { get; set; }
        public int genderID { get; set; }
        public int affiliateID { get; set; }
        public int marketingID { get; set; }
        public int quidcoID { get; set; }
        public int memberReferralMemberID { get; set; }
        public string memberReferralCode { get; set; }
        public string memberSourceID { get; set; }
        public string memberForename { get; set; }
        public string memberSurname { get; set; }
        public string memberAddress1 { get; set; }
        public string memberAddress2 { get; set; }
        public string memberAddress3 { get; set; }
        public string memberAddress4 { get; set; }
        public string memberAddress5 { get; set; }
        public string memberPostcode { get; set; }
        public System.DateTime memberDob { get; set; }
        public System.DateTime memberUnsubscribeDate { get; set; }
        public bool memberUnsubscribe { get; set; }
        public string memberUnsubscribeReason { get; set; }
        public bool memberPremium { get; set; }
        public bool memberActivated { get; set; }
        public System.Guid memberEmailVerification { get; set; }
        public Nullable<decimal> memberTermsVersion { get; set; }
    }
}
