﻿<connectionStrings>
  <add name="Redis" connectionString="neu-tstdev-r4g-redis.redis.cache.windows.net:6380,password=UX0Zr96h3Q6svJpS4l0iuRWd3LpjdvrNrpqTlIhYEeE=,ssl=True,abortConnect=False"/>
  <add name="ClubsEntities" connectionString="metadata=res://*/Data.ClubsModel.csdl|res://*/Data.ClubsModel.ssdl|res://*/Data.ClubsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=Clubs;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="CMS" connectionString="Initial Catalog=CMS;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
  <add name="CMSEntities" connectionString="metadata=res://*/Data.LegacyCmsModel.csdl|res://*/Data.LegacyCmsModel.ssdl|res://*/Data.LegacyCmsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=CMS;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />  <add name="CMSEntities2" connectionString="metadata=res://*/Cms_Model.csdl|res://*/Cms_Model.ssdl|res://*/Cms_Model.msl;provider=System.Data.SqlClient;provider connection string='data source=sqlhosting3.rewards4group.com;initial catalog=CMS;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework'" providerName="System.Data.EntityClient" />
  <add name="HangfireTaskScheduler" connectionString="Initial Catalog=TaskScheduler_Websites;Data Source=NEU-TSTDEV-SQL3\\TEST;Integrated Security=SSPI"/>
  <add name="InsightTeamEntities" connectionString="metadata=res://*/App_Code.InsightTeamModel.csdl|res://*/App_Code.InsightTeamModel.ssdl|res://*/App_Code.InsightTeamModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=InsightTeam;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="Logging" connectionString="Initial Catalog=Logging;Data Source=NEU-TSTDEV-SQL3\\TEST;Integrated Security=SSPI" />
  <add name="LoggingEntities" connectionString="metadata=res://*/Data.LoggingModel.csdl|res://*/Data.LoggingModel.ssdl|res://*/Data.LoggingModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=Logging;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />  
  <add name="MemberProfile" connectionString="Initial Catalog=MemberProfile;Data Source=NEU-TSTDEV-SQL3\\TEST;Integrated Security=SSPI" />
  <add name="MemberProfileEntities" connectionString="metadata=res://*/Data.MemberProfileModel.csdl|res://*/Data.MemberProfileModel.ssdl|res://*/Data.MemberProfileModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=MemberProfile;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
	<!--<add name="Membership" connectionString="Initial Catalog=Membership;Data Source=NEU-TSTDEV-SQL3\\TEST;Integrated Security=SSPI" />-->
	<add name="Membership" connectionString="Initial Catalog=Membership;Data Source=NEU-TSTDEV-SQL3\\TEST;Integrated Security=SSPI;Persist Security Info=True;TrustServerCertificate=True;" />
  <add name="MembershipEntities1" connectionString="metadata=res://*/Data.AspNetMembershipModel.csdl|res://*/Data.AspNetMembershipModel.ssdl|res://*/Data.AspNetMembershipModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=NEU-TSTDEV-SQL3\\TEST;initial catalog=Membership;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="Quidco" connectionString="Initial Catalog=Quidco;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
</connectionStrings>

