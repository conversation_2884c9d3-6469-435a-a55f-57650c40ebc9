﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using FluentValidation.Results;
using Newtonsoft.Json;

namespace Rewards4Sport.Membership.Api.Models.NewMember
{
    public class NewMemberResponse
    {
        public bool IsSuccess { get; set; }
        public int MemberId { get; set; }
        public Guid MemberGuid { get; set; }
        public string Forename { get; set; }
        public string Application { get; set; }
        public ValidationResult ValidationResult { get; set; }
    }
}