﻿using System;
using System.Configuration;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Hangfire;
using Hangfire.Pro.Redis;
using Hangfire.StructureMap;
using Microsoft.Owin;
using Owin;
using Rewards4Sport.Membership.Api.DependencyResolution;

[assembly: OwinStartup(typeof(Rewards4Sport.Membership.Api.Startup))]

namespace Rewards4Sport.Membership.Api
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            //For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=316888

            var redisOptions = new RedisStorageOptions()
            {
                Database = 0,
                Prefix = "hangfire:membership:" + ConfigurationManager.AppSettings["Redis:Database"] + ":"
            };

            redisOptions.CertificateValidation += ValidateServerCertificate;

            GlobalConfiguration.Configuration
                .UseRedisStorage(ConfigurationManager.ConnectionStrings["Redis"].ConnectionString, redisOptions);

            var container = IoC.Initialize();

            GlobalConfiguration.Configuration.UseStructureMapActivator(container);

            app.UseHangfireDashboard();
            app.UseHangfireServer();
        }

        /// <summary>
        /// Validates the SSL certificate received during the Redis connection attempt.
        /// This method is specifically designed to handle certificate validation for connections
        /// to Azure Cache for Redis instances via Azure Private Link, where a common name mismatch
        /// might occur due to the private link DNS name.
        /// </summary>
        /// <param name="sender">The sender object initiating the certificate validation request.</param>
        /// <param name="certificate">The certificate to validate.</param>
        /// <param name="chain">The chain of certificate authorities associated with the certificate.</param>
        /// <param name="sslPolicyErrors">One or more errors associated with the SSL policy.</param>
        /// <returns>
        /// Returns <c>true</c> if the certificate is valid or the error is specifically related to
        /// a CN mismatch that is acceptable for Azure Redis Cache connections via private link.
        /// Returns <c>false</c> otherwise, indicating that the certificate should not be trusted.
        /// </returns>
        public static bool ValidateServerCertificate(
        object sender,
        X509Certificate certificate,
        X509Chain chain,
        SslPolicyErrors sslPolicyErrors)
        {
            if (sslPolicyErrors == SslPolicyErrors.None)
                return true;

            if (certificate.Subject.Contains("CN=*.redis.cache.windows.net") && sslPolicyErrors == SslPolicyErrors.RemoteCertificateNameMismatch)
                return true;

            return false;
        }
    }
}