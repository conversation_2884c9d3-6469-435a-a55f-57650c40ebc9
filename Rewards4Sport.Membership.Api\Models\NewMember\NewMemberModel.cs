﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using FluentValidation;
using FluentValidation.Validators;

namespace Rewards4Sport.Membership.Api.Models
{
    public class NewMemberModel
    {
        public string Forename { get; set; }
        public string Surname { get; set; } = string.Empty;
        public string Username { get; set; }
        public string Password { get; set; }
        public string Application { get; set; }
        public string MemberSourceId { get; set; }
        public int ClubId { get; set; }
        public string TeamName { get; set; }
        public int AffiliateId { get; set; } = 0;
        public string MobileNumber { get; set; } = string.Empty;
        /// <summary>
        /// This is the memberId of the person who has referred the new member to the programme
        /// </summary>
        public int ReferralMemberID { get; set; } = 0;
        public string RegistrationPageUrl { get; set; }
        public decimal MemberTermsVersion { get; set; } = 0;
        public string RemoteIpAddress { get; set; }
    }

    public class NewMemberModelValidator : AbstractValidator<NewMemberModel>
    {
        public NewMemberModelValidator()
        {
            RuleFor(member => member.Forename)
                .NotNull()
                .MinimumLength(2)
                .MaximumLength(25);

            RuleFor(member => member.Username)
                .NotNull()
                .EmailAddress(EmailValidationMode.Net4xRegex);

            RuleFor(member => member.Password)
                .NotNull()
                .Matches(Constants.PasswordComplexity)
                .MaximumLength(50);

            RuleFor(member => member.Application)
                .NotNull()
                .Matches("^(?=.*?[a-z])(?=.*?[0-9]).{8,}$")
                .MaximumLength(50)
                .Must(application => Constants.ApplicationList.Contains(application.ToLower()));

            RuleFor(member => member.MemberSourceId)
                .NotNull()
                .MinimumLength(3)
                .MaximumLength(100);

            RuleFor(member => member.AffiliateId)
                .NotNull();

            RuleFor(member => member.RegistrationPageUrl)
                .NotNull();

            RuleFor(member => member.MemberTermsVersion)
                .GreaterThan(0);

            RuleFor(member => member.RemoteIpAddress)
                .NotNull();

        }
    }
}