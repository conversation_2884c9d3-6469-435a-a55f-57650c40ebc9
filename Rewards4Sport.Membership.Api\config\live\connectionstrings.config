﻿<connectionStrings>
  <add name="Redis" connectionString="neu-prd-r4g-redis.redis.cache.windows.net:6380,password=lSTchGiQOosnecG9tT3mTUSzykQpI0defEtiU3ST3zU=,ssl=True,abortConnect=False"/>
  <add name="ClubsEntities" connectionString="metadata=res://*/Data.ClubsModel.csdl|res://*/Data.ClubsModel.ssdl|res://*/Data.ClubsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Clubs;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="CMS" connectionString="Initial Catalog=CMS;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
  <add name="CMSEntities" connectionString="metadata=res://*/Data.LegacyCmsModel.csdl|res://*/Data.LegacyCmsModel.ssdl|res://*/Data.LegacyCmsModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=CMS;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="HangfireTaskScheduler" connectionString="Initial Catalog=TaskScheduler_Websites;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI"/>
  <add name="InsightTeamEntities" connectionString="metadata=res://*/App_Code.InsightTeamModel.csdl|res://*/App_Code.InsightTeamModel.ssdl|res://*/App_Code.InsightTeamModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=InsightTeam;persist security info=True;Integrated Security=SSPI;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="Logging" connectionString="Initial Catalog=Logging;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
  <add name="LoggingEntities" connectionString="metadata=res://*/Data.LoggingModel.csdl|res://*/Data.LoggingModel.ssdl|res://*/Data.LoggingModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Logging;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="MemberProfile" connectionString="Initial Catalog=MemberProfile;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
  <add name="MemberProfileEntities" connectionString="metadata=res://*/Data.MemberProfileModel.csdl|res://*/Data.MemberProfileModel.ssdl|res://*/Data.MemberProfileModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=MemberProfile;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="Membership" connectionString="Initial Catalog=Membership;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
  <add name="MembershipEntities1" connectionString="metadata=res://*/Data.AspNetMembershipModel.csdl|res://*/Data.AspNetMembershipModel.ssdl|res://*/Data.AspNetMembershipModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sqlhosting3.rewards4group.com;initial catalog=Membership;persist security info=True;Integrated Security=SSPI;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  <add name="Quidco" connectionString="Initial Catalog=Quidco;data source=sqlhosting3.rewards4group.com;Integrated Security=SSPI" />
</connectionStrings>

