﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="ClubsModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="ClubApplications">
          <Key>
            <PropertyRef Name="linkID" />
          </Key>
          <Property Name="linkID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ApplicationName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="linkSuppress" Type="bit" Nullable="false" />
          <Property Name="linkDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="ClubMembers">
          <Key>
            <PropertyRef Name="linkID" />
          </Key>
          <Property Name="linkID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="memberID" Type="int" Nullable="false" />
          <Property Name="clubID" Type="int" Nullable="false" />
          <Property Name="linkDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="PartnerClubs">
          <Key>
            <PropertyRef Name="clubID" />
          </Key>
          <Property Name="clubID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ApplicationName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="clubName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="clubStadium" Type="varchar" MaxLength="255" />
          <Property Name="clubUrl" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="clubCssPath" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="clubLogo" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="clubDate" Type="datetime" Nullable="false" />
          <Property Name="clubSuppress" Type="bit" Nullable="false" />
        </EntityType>
        <EntityContainer Name="ClubsModelStoreContainer">
          <EntitySet Name="ClubApplications" EntityType="Self.ClubApplications" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ClubMembers" EntityType="Self.ClubMembers" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PartnerClubs" EntityType="Self.PartnerClubs" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="ClubsModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="ClubApplication">
          <Key>
            <PropertyRef Name="linkID" />
          </Key>
          <Property Name="linkID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ApplicationName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="linkSuppress" Type="Boolean" Nullable="false" />
          <Property Name="linkDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="ClubMember">
          <Key>
            <PropertyRef Name="linkID" />
          </Key>
          <Property Name="linkID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="memberID" Type="Int32" Nullable="false" />
          <Property Name="clubID" Type="Int32" Nullable="false" />
          <Property Name="linkDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="PartnerClub">
          <Key>
            <PropertyRef Name="clubID" />
          </Key>
          <Property Name="clubID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ApplicationName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="clubName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="clubStadium" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="clubUrl" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="clubCssPath" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="clubLogo" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="clubDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="clubSuppress" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityContainer Name="ClubsEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="ClubApplications" EntityType="Self.ClubApplication" />
          <EntitySet Name="ClubMembers" EntityType="Self.ClubMember" />
          <EntitySet Name="PartnerClubs" EntityType="Self.PartnerClub" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="ClubsModelStoreContainer" CdmEntityContainer="ClubsEntities">
          <EntitySetMapping Name="ClubApplications">
            <EntityTypeMapping TypeName="ClubsModel.ClubApplication">
              <MappingFragment StoreEntitySet="ClubApplications">
                <ScalarProperty Name="linkID" ColumnName="linkID" />
                <ScalarProperty Name="ApplicationName" ColumnName="ApplicationName" />
                <ScalarProperty Name="linkSuppress" ColumnName="linkSuppress" />
                <ScalarProperty Name="linkDate" ColumnName="linkDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ClubMembers">
            <EntityTypeMapping TypeName="ClubsModel.ClubMember">
              <MappingFragment StoreEntitySet="ClubMembers">
                <ScalarProperty Name="linkID" ColumnName="linkID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="clubID" ColumnName="clubID" />
                <ScalarProperty Name="linkDate" ColumnName="linkDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PartnerClubs">
            <EntityTypeMapping TypeName="ClubsModel.PartnerClub">
              <MappingFragment StoreEntitySet="PartnerClubs">
                <ScalarProperty Name="clubID" ColumnName="clubID" />
                <ScalarProperty Name="ApplicationName" ColumnName="ApplicationName" />
                <ScalarProperty Name="clubName" ColumnName="clubName" />
                <ScalarProperty Name="clubStadium" ColumnName="clubStadium" />
                <ScalarProperty Name="clubUrl" ColumnName="clubUrl" />
                <ScalarProperty Name="clubCssPath" ColumnName="clubCssPath" />
                <ScalarProperty Name="clubLogo" ColumnName="clubLogo" />
                <ScalarProperty Name="clubDate" ColumnName="clubDate" />
                <ScalarProperty Name="clubSuppress" ColumnName="clubSuppress" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>