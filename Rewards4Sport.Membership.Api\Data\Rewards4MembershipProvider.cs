﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Web.Profile;
using System.Web.Security;

namespace DAL2016
{
    public class SecureMembershipProvider : SqlMembershipProvider
    {
        public override void Initialize(string name, System.Collections.Specialized.NameValueCollection config)
        {
            base.Initialize(name, config);

            // Update the private connection string field in the base class.
            string connectionString = ConfigurationManager.ConnectionStrings["Membership"].ConnectionString; // ConnectionManager.GetConnectionString(base.Name);

            // Set private property of Membership provider.
            FieldInfo connectionStringField = GetType().BaseType.GetField("_sqlConnectionString", BindingFlags.Instance | BindingFlags.NonPublic);
            connectionStringField.SetValue(this, connectionString);
        }
    }

    public class SecureProfileProvider : SqlProfileProvider
    {
        public override void Initialize(string name, System.Collections.Specialized.NameValueCollection config)
        {
            base.Initialize(name, config);

            // Update the private connection string field in the base class.
            string connectionString = ConfigurationManager.ConnectionStrings["Membership"].ConnectionString; // ConnectionManager.GetConnectionString(base.Name);

            // Set private property of Membership provider.
            FieldInfo connectionStringField = GetType().BaseType.GetField("_sqlConnectionString", BindingFlags.Instance | BindingFlags.NonPublic);
            connectionStringField.SetValue(this, connectionString);
        }
    }

    public class SecureRoleProvider : SqlRoleProvider
    {
        public override void Initialize(string name, System.Collections.Specialized.NameValueCollection config)
        {
            string sAppName = config["ApplicationName"];

            config.Remove("ApplicationName");

            base.Initialize(name, config);

            base.ApplicationName = sAppName;

            // Update the private connection string field in the base class.
            string connectionString = ConfigurationManager.ConnectionStrings["Membership"].ConnectionString; // ConnectionManager.GetConnectionString(base.Name);

            // Set private property of Membership provider.
            FieldInfo connectionStringField = GetType().BaseType.GetField("_sqlConnectionString", BindingFlags.Instance | BindingFlags.NonPublic);
            connectionStringField.SetValue(this, connectionString);
        }
    }
}