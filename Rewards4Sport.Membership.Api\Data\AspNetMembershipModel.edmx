﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="AspNetMembershipModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="Affiliates">
          <Key>
            <PropertyRef Name="affiliateID" />
          </Key>
          <Property Name="affiliateID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="clubID" Type="int" />
          <Property Name="applicationName" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="affiliateUsername" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="affiliatePassword" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="affiliateDescription" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="affiliateDate" Type="datetime" Nullable="false" />
          <Property Name="affiliateTest" Type="bit" Nullable="false" />
          <Property Name="affiliateSuppress" Type="bit" Nullable="false" />
          <Property Name="affiliateDisplayInAdminSystem" Type="bit" Nullable="false" />
          <Property Name="affiliatePointsPerPoundEarningRate" Type="money" />
          <Property Name="affiliatePointsPerPoundRedeemRate" Type="money" />
          <Property Name="affiliateTransactionPointsCap" Type="int" />
          <Property Name="affiliateMemberPointsCap" Type="int" />
          <Property Name="affiliateMarketingOptIn" Type="bit" />
          <Property Name="affiliateDisableMemberRegistration" Type="bit" Nullable="false" />
          <Property Name="affiliatePointsStatusApplication" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="aspnet_Applications">
          <Key>
            <PropertyRef Name="ApplicationId" />
          </Key>
          <Property Name="ApplicationName" Type="nvarchar" MaxLength="256" Nullable="false" />
          <Property Name="LoweredApplicationName" Type="nvarchar" MaxLength="256" Nullable="false" />
          <Property Name="ApplicationId" Type="uniqueidentifier" Nullable="false" />
          <Property Name="Description" Type="nvarchar" MaxLength="256" />
        </EntityType>
        <EntityType Name="aspnet_Membership">
          <Key>
            <PropertyRef Name="UserId" />
          </Key>
          <Property Name="ApplicationId" Type="uniqueidentifier" Nullable="false" />
          <Property Name="UserId" Type="uniqueidentifier" Nullable="false" />
          <Property Name="Password" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="PasswordFormat" Type="int" Nullable="false" />
          <Property Name="PasswordSalt" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="MobilePIN" Type="nvarchar" MaxLength="16" />
          <Property Name="Email" Type="nvarchar" MaxLength="256" />
          <Property Name="LoweredEmail" Type="nvarchar" MaxLength="256" />
          <Property Name="PasswordQuestion" Type="nvarchar" MaxLength="256" />
          <Property Name="PasswordAnswer" Type="nvarchar" MaxLength="128" />
          <Property Name="IsApproved" Type="bit" Nullable="false" />
          <Property Name="IsLockedOut" Type="bit" Nullable="false" />
          <Property Name="CreateDate" Type="datetime" Nullable="false" />
          <Property Name="LastLoginDate" Type="datetime" Nullable="false" />
          <Property Name="LastPasswordChangedDate" Type="datetime" Nullable="false" />
          <Property Name="LastLockoutDate" Type="datetime" Nullable="false" />
          <Property Name="FailedPasswordAttemptCount" Type="int" Nullable="false" />
          <Property Name="FailedPasswordAttemptWindowStart" Type="datetime" Nullable="false" />
          <Property Name="FailedPasswordAnswerAttemptCount" Type="int" Nullable="false" />
          <Property Name="FailedPasswordAnswerAttemptWindowStart" Type="datetime" Nullable="false" />
          <Property Name="Comment" Type="ntext" />
        </EntityType>
        <EntityType Name="aspnet_SchemaVersions">
          <Key>
            <PropertyRef Name="Feature" />
            <PropertyRef Name="CompatibleSchemaVersion" />
          </Key>
          <Property Name="Feature" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="CompatibleSchemaVersion" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="IsCurrentVersion" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="aspnet_Usernames">
          <Key>
            <PropertyRef Name="usernameID" />
          </Key>
          <Property Name="usernameID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ApplicationName" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberID" Type="int" Nullable="false" />
          <Property Name="UserID" Type="uniqueidentifier" Nullable="false" />
          <Property Name="usernameLogin" Type="uniqueidentifier" Nullable="false" />
          <Property Name="usernameEmail" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="usernameEmail512" Type="varchar" MaxLength="128" />
          <Property Name="usernameEmail1" Type="varchar" MaxLength="40" />
          <Property Name="usernameEmailUTF32" Type="varchar" MaxLength="64" />
          <Property Name="usernameMobile" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="usernameDate" Type="datetime" Nullable="false" />
          <Property Name="usernameSuppress" Type="bit" Nullable="false" />
          <Property Name="usernameHashChecked" Type="datetime" />
        </EntityType>
        <EntityType Name="aspnet_Users">
          <Key>
            <PropertyRef Name="UserId" />
          </Key>
          <Property Name="ApplicationId" Type="uniqueidentifier" Nullable="false" />
          <Property Name="UserId" Type="uniqueidentifier" Nullable="false" />
          <Property Name="UserName" Type="nvarchar" MaxLength="256" Nullable="false" />
          <Property Name="LoweredUserName" Type="nvarchar" MaxLength="256" Nullable="false" />
          <Property Name="MobileAlias" Type="nvarchar" MaxLength="16" />
          <Property Name="IsAnonymous" Type="bit" Nullable="false" />
          <Property Name="LastActivityDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="Members">
          <Key>
            <PropertyRef Name="memberID" />
          </Key>
          <Property Name="memberID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserID" Type="uniqueidentifier" Nullable="false" />
          <Property Name="membertypeID" Type="int" Nullable="false" />
          <Property Name="titleID" Type="int" Nullable="false" />
          <Property Name="genderID" Type="int" Nullable="false" />
          <Property Name="affiliateID" Type="int" Nullable="false" />
          <Property Name="marketingID" Type="int" Nullable="false" />
          <Property Name="quidcoID" Type="int" Nullable="false" />
          <Property Name="memberReferralMemberID" Type="int" Nullable="false" />
          <Property Name="memberReferralCode" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberSourceID" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberForename" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberSurname" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberAddress1" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberAddress2" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberAddress3" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberAddress4" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberAddress5" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberPostcode" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="memberDob" Type="datetime" Nullable="false" />
          <Property Name="memberUnsubscribeDate" Type="datetime" Nullable="false" />
          <Property Name="memberUnsubscribe" Type="bit" Nullable="false" />
          <Property Name="memberUnsubscribeReason" Type="varchar" MaxLength="50" />
          <Property Name="memberPremium" Type="bit" Nullable="false" />
          <Property Name="memberActivated" Type="bit" Nullable="false" />
          <Property Name="memberEmailVerification" Type="uniqueidentifier" Nullable="false" />
          <Property Name="memberTermsVersion" Type="money" />
        </EntityType>
        <Association Name="FK__aspnet_Me__Appli__276EDEB3">
          <End Role="aspnet_Applications" Type="Self.aspnet_Applications" Multiplicity="1" />
          <End Role="aspnet_Membership" Type="Self.aspnet_Membership" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Applications">
              <PropertyRef Name="ApplicationId" />
            </Principal>
            <Dependent Role="aspnet_Membership">
              <PropertyRef Name="ApplicationId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__aspnet_Me__UserI__286302EC">
          <End Role="aspnet_Users" Type="Self.aspnet_Users" Multiplicity="1" />
          <End Role="aspnet_Membership" Type="Self.aspnet_Membership" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Users">
              <PropertyRef Name="UserId" />
            </Principal>
            <Dependent Role="aspnet_Membership">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__aspnet_Us__Appli__173876EA">
          <End Role="aspnet_Applications" Type="Self.aspnet_Applications" Multiplicity="1" />
          <End Role="aspnet_Users" Type="Self.aspnet_Users" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Applications">
              <PropertyRef Name="ApplicationId" />
            </Principal>
            <Dependent Role="aspnet_Users">
              <PropertyRef Name="ApplicationId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Function Name="aspnet_Membership_CreateUser" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="Password" Type="nvarchar" Mode="In" />
          <Parameter Name="PasswordSalt" Type="nvarchar" Mode="In" />
          <Parameter Name="Email" Type="nvarchar" Mode="In" />
          <Parameter Name="PasswordQuestion" Type="nvarchar" Mode="In" />
          <Parameter Name="PasswordAnswer" Type="nvarchar" Mode="In" />
          <Parameter Name="IsApproved" Type="bit" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="CreateDate" Type="datetime" Mode="In" />
          <Parameter Name="UniqueEmail" Type="int" Mode="In" />
          <Parameter Name="PasswordFormat" Type="int" Mode="In" />
          <Parameter Name="UserId" Type="uniqueidentifier" Mode="InOut" />
        </Function>
        <Function Name="aspnet_Membership_FindUsersByEmail" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="EmailToMatch" Type="nvarchar" Mode="In" />
          <Parameter Name="PageIndex" Type="int" Mode="In" />
          <Parameter Name="PageSize" Type="int" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_FindUsersByName" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserNameToMatch" Type="nvarchar" Mode="In" />
          <Parameter Name="PageIndex" Type="int" Mode="In" />
          <Parameter Name="PageSize" Type="int" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetAllUsers" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="PageIndex" Type="int" Mode="In" />
          <Parameter Name="PageSize" Type="int" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetNumberOfUsersOnline" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="MinutesSinceLastInActive" Type="int" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetPassword" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="MaxInvalidPasswordAttempts" Type="int" Mode="In" />
          <Parameter Name="PasswordAttemptWindow" Type="int" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="PasswordAnswer" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetPasswordWithFormat" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="UpdateLastLoginActivityDate" Type="bit" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetUserByEmail" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="Email" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetUserByName" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="UpdateLastActivity" Type="bit" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_GetUserByUserId" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="UserId" Type="uniqueidentifier" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="UpdateLastActivity" Type="bit" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_ResetPassword" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="NewPassword" Type="nvarchar" Mode="In" />
          <Parameter Name="MaxInvalidPasswordAttempts" Type="int" Mode="In" />
          <Parameter Name="PasswordAttemptWindow" Type="int" Mode="In" />
          <Parameter Name="PasswordSalt" Type="nvarchar" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="PasswordFormat" Type="int" Mode="In" />
          <Parameter Name="PasswordAnswer" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_SetPassword" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="NewPassword" Type="nvarchar" Mode="In" />
          <Parameter Name="PasswordSalt" Type="nvarchar" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
          <Parameter Name="PasswordFormat" Type="int" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_UnlockUser" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="aspnet_Membership_UpdateUser" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="Email" Type="nvarchar" Mode="In" />
          <Parameter Name="Comment" Type="ntext" Mode="In" />
          <Parameter Name="IsApproved" Type="bit" Mode="In" />
          <Parameter Name="LastLoginDate" Type="datetime" Mode="In" />
          <Parameter Name="LastActivityDate" Type="datetime" Mode="In" />
          <Parameter Name="UniqueEmail" Type="int" Mode="In" />
          <Parameter Name="CurrentTimeUtc" Type="datetime" Mode="In" />
        </Function>
        <Function Name="aspnet_Users_CreateUser" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationId" Type="uniqueidentifier" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="IsUserAnonymous" Type="bit" Mode="In" />
          <Parameter Name="LastActivityDate" Type="datetime" Mode="In" />
          <Parameter Name="UserId" Type="uniqueidentifier" Mode="InOut" />
        </Function>
        <Function Name="aspnet_Users_DeleteUser" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="ApplicationName" Type="nvarchar" Mode="In" />
          <Parameter Name="UserName" Type="nvarchar" Mode="In" />
          <Parameter Name="TablesToDeleteFrom" Type="int" Mode="In" />
          <Parameter Name="NumTablesDeletedFrom" Type="int" Mode="InOut" />
        </Function>
        <Function Name="CheckMemberApplication" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="MemberID" Type="int" Mode="In" />
          <Parameter Name="ApplicationName" Type="varchar" Mode="In" />
        </Function>
        <Function Name="GetAffiliateMemberSources_ByAffiliateIDMemberID" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="affiliateID" Type="int" Mode="In" />
          <Parameter Name="memberID" Type="int" Mode="In" />
        </Function>
        <Function Name="GetAffiliateMemberSources_ByAffiliateIDValue" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="affiliateID" Type="int" Mode="In" />
          <Parameter Name="Value" Type="varchar" Mode="In" />
        </Function>
        <Function Name="GetAffiliateMemberSources_ByMemberID" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="memberID" Type="int" Mode="In" />
        </Function>
        <EntityContainer Name="AspNetMembershipModelStoreContainer">
          <EntitySet Name="Affiliates" EntityType="Self.Affiliates" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="aspnet_Applications" EntityType="Self.aspnet_Applications" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="aspnet_Membership" EntityType="Self.aspnet_Membership" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="aspnet_SchemaVersions" EntityType="Self.aspnet_SchemaVersions" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="aspnet_Usernames" EntityType="Self.aspnet_Usernames" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="aspnet_Users" EntityType="Self.aspnet_Users" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Members" EntityType="Self.Members" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="FK__aspnet_Me__Appli__276EDEB3" Association="Self.FK__aspnet_Me__Appli__276EDEB3">
            <End Role="aspnet_Applications" EntitySet="aspnet_Applications" />
            <End Role="aspnet_Membership" EntitySet="aspnet_Membership" />
          </AssociationSet>
          <AssociationSet Name="FK__aspnet_Me__UserI__286302EC" Association="Self.FK__aspnet_Me__UserI__286302EC">
            <End Role="aspnet_Users" EntitySet="aspnet_Users" />
            <End Role="aspnet_Membership" EntitySet="aspnet_Membership" />
          </AssociationSet>
          <AssociationSet Name="FK__aspnet_Us__Appli__173876EA" Association="Self.FK__aspnet_Us__Appli__173876EA">
            <End Role="aspnet_Applications" EntitySet="aspnet_Applications" />
            <End Role="aspnet_Users" EntitySet="aspnet_Users" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="AspNetMembershipModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="aspnet_Applications">
          <Key>
            <PropertyRef Name="ApplicationId" />
          </Key>
          <Property Name="ApplicationName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LoweredApplicationName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ApplicationId" Type="Guid" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="aspnet_Membership" Relationship="Self.FK__aspnet_Me__Appli__276EDEB3" FromRole="aspnet_Applications" ToRole="aspnet_Membership" />
          <NavigationProperty Name="aspnet_Users" Relationship="Self.FK__aspnet_Us__Appli__173876EA" FromRole="aspnet_Applications" ToRole="aspnet_Users" />
        </EntityType>
        <EntityType Name="aspnet_Membership">
          <Key>
            <PropertyRef Name="UserId" />
          </Key>
          <Property Name="ApplicationId" Type="Guid" Nullable="false" />
          <Property Name="UserId" Type="Guid" Nullable="false" />
          <Property Name="Password" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="PasswordFormat" Type="Int32" Nullable="false" />
          <Property Name="PasswordSalt" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="MobilePIN" Type="String" MaxLength="16" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="LoweredEmail" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="PasswordQuestion" Type="String" MaxLength="256" FixedLength="false" Unicode="true" />
          <Property Name="PasswordAnswer" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="IsApproved" Type="Boolean" Nullable="false" />
          <Property Name="IsLockedOut" Type="Boolean" Nullable="false" />
          <Property Name="CreateDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="LastLoginDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="LastPasswordChangedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="LastLockoutDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="FailedPasswordAttemptCount" Type="Int32" Nullable="false" />
          <Property Name="FailedPasswordAttemptWindowStart" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="FailedPasswordAnswerAttemptCount" Type="Int32" Nullable="false" />
          <Property Name="FailedPasswordAnswerAttemptWindowStart" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Comment" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="aspnet_Applications" Relationship="Self.FK__aspnet_Me__Appli__276EDEB3" FromRole="aspnet_Membership" ToRole="aspnet_Applications" />
          <NavigationProperty Name="aspnet_Users" Relationship="Self.FK__aspnet_Me__UserI__286302EC" FromRole="aspnet_Membership" ToRole="aspnet_Users" />
        </EntityType>
        <EntityType Name="aspnet_SchemaVersions">
          <Key>
            <PropertyRef Name="Feature" />
            <PropertyRef Name="CompatibleSchemaVersion" />
          </Key>
          <Property Name="Feature" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="CompatibleSchemaVersion" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="IsCurrentVersion" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="aspnet_Usernames">
          <Key>
            <PropertyRef Name="usernameID" />
          </Key>
          <Property Name="usernameID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ApplicationName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="memberID" Type="Int32" Nullable="false" />
          <Property Name="UserID" Type="Guid" Nullable="false" />
          <Property Name="usernameLogin" Type="Guid" Nullable="false" />
          <Property Name="usernameEmail" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="usernameEmail512" Type="String" MaxLength="128" FixedLength="false" Unicode="false" />
          <Property Name="usernameEmail1" Type="String" MaxLength="40" FixedLength="false" Unicode="false" />
          <Property Name="usernameEmailUTF32" Type="String" MaxLength="64" FixedLength="false" Unicode="false" />
          <Property Name="usernameMobile" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="usernameDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="usernameSuppress" Type="Boolean" Nullable="false" />
          <Property Name="usernameHashChecked" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="aspnet_Users">
          <Key>
            <PropertyRef Name="UserId" />
          </Key>
          <Property Name="ApplicationId" Type="Guid" Nullable="false" />
          <Property Name="UserId" Type="Guid" Nullable="false" />
          <Property Name="UserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LoweredUserName" Type="String" MaxLength="256" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="MobileAlias" Type="String" MaxLength="16" FixedLength="false" Unicode="true" />
          <Property Name="IsAnonymous" Type="Boolean" Nullable="false" />
          <Property Name="LastActivityDate" Type="DateTime" Nullable="false" Precision="3" />
          <NavigationProperty Name="aspnet_Applications" Relationship="Self.FK__aspnet_Us__Appli__173876EA" FromRole="aspnet_Users" ToRole="aspnet_Applications" />
          <NavigationProperty Name="aspnet_Membership" Relationship="Self.FK__aspnet_Me__UserI__286302EC" FromRole="aspnet_Users" ToRole="aspnet_Membership" />
        </EntityType>
        <Association Name="FK__aspnet_Me__Appli__276EDEB3">
          <End Role="aspnet_Applications" Type="Self.aspnet_Applications" Multiplicity="1" />
          <End Role="aspnet_Membership" Type="Self.aspnet_Membership" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Applications">
              <PropertyRef Name="ApplicationId" />
            </Principal>
            <Dependent Role="aspnet_Membership">
              <PropertyRef Name="ApplicationId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__aspnet_Us__Appli__173876EA">
          <End Role="aspnet_Applications" Type="Self.aspnet_Applications" Multiplicity="1" />
          <End Role="aspnet_Users" Type="Self.aspnet_Users" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Applications">
              <PropertyRef Name="ApplicationId" />
            </Principal>
            <Dependent Role="aspnet_Users">
              <PropertyRef Name="ApplicationId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__aspnet_Me__UserI__286302EC">
          <End Role="aspnet_Users" Type="Self.aspnet_Users" Multiplicity="1" />
          <End Role="aspnet_Membership" Type="Self.aspnet_Membership" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="aspnet_Users">
              <PropertyRef Name="UserId" />
            </Principal>
            <Dependent Role="aspnet_Membership">
              <PropertyRef Name="UserId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="MembershipEntities1" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="aspnet_Applications" EntityType="Self.aspnet_Applications" />
          <EntitySet Name="aspnet_Membership" EntityType="Self.aspnet_Membership" />
          <EntitySet Name="aspnet_SchemaVersions" EntityType="Self.aspnet_SchemaVersions" />
          <EntitySet Name="aspnet_Usernames" EntityType="Self.aspnet_Usernames" />
          <EntitySet Name="aspnet_Users" EntityType="Self.aspnet_Users" />
          <AssociationSet Name="FK__aspnet_Me__Appli__276EDEB3" Association="Self.FK__aspnet_Me__Appli__276EDEB3">
            <End Role="aspnet_Applications" EntitySet="aspnet_Applications" />
            <End Role="aspnet_Membership" EntitySet="aspnet_Membership" />
          </AssociationSet>
          <AssociationSet Name="FK__aspnet_Us__Appli__173876EA" Association="Self.FK__aspnet_Us__Appli__173876EA">
            <End Role="aspnet_Applications" EntitySet="aspnet_Applications" />
            <End Role="aspnet_Users" EntitySet="aspnet_Users" />
          </AssociationSet>
          <AssociationSet Name="FK__aspnet_Me__UserI__286302EC" Association="Self.FK__aspnet_Me__UserI__286302EC">
            <End Role="aspnet_Users" EntitySet="aspnet_Users" />
            <End Role="aspnet_Membership" EntitySet="aspnet_Membership" />
          </AssociationSet>
          <EntitySet Name="Members" EntityType="AspNetMembershipModel.Member" />
          <EntitySet Name="Affiliates" EntityType="AspNetMembershipModel.Affiliate" />
          <FunctionImport Name="aspnet_Membership_CreateUser">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="Password" Mode="In" Type="String" />
            <Parameter Name="PasswordSalt" Mode="In" Type="String" />
            <Parameter Name="Email" Mode="In" Type="String" />
            <Parameter Name="PasswordQuestion" Mode="In" Type="String" />
            <Parameter Name="PasswordAnswer" Mode="In" Type="String" />
            <Parameter Name="IsApproved" Mode="In" Type="Boolean" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="CreateDate" Mode="In" Type="DateTime" />
            <Parameter Name="UniqueEmail" Mode="In" Type="Int32" />
            <Parameter Name="PasswordFormat" Mode="In" Type="Int32" />
            <Parameter Name="UserId" Mode="InOut" Type="Guid" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_FindUsersByEmail">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="EmailToMatch" Mode="In" Type="String" />
            <Parameter Name="PageIndex" Mode="In" Type="Int32" />
            <Parameter Name="PageSize" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_FindUsersByName">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserNameToMatch" Mode="In" Type="String" />
            <Parameter Name="PageIndex" Mode="In" Type="Int32" />
            <Parameter Name="PageSize" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetAllUsers">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="PageIndex" Mode="In" Type="Int32" />
            <Parameter Name="PageSize" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetNumberOfUsersOnline">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="MinutesSinceLastInActive" Mode="In" Type="Int32" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetPassword" ReturnType="Collection(AspNetMembershipModel.aspnet_Membership_GetPassword_Result)">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="MaxInvalidPasswordAttempts" Mode="In" Type="Int32" />
            <Parameter Name="PasswordAttemptWindow" Mode="In" Type="Int32" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="PasswordAnswer" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetPasswordWithFormat" ReturnType="Collection(AspNetMembershipModel.aspnet_Membership_GetPasswordWithFormat_Result)">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="UpdateLastLoginActivityDate" Mode="In" Type="Boolean" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetUserByEmail" ReturnType="Collection(String)">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="Email" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetUserByName" ReturnType="Collection(AspNetMembershipModel.aspnet_Membership_GetUserByName_Result)">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="UpdateLastActivity" Mode="In" Type="Boolean" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_GetUserByUserId" ReturnType="Collection(AspNetMembershipModel.aspnet_Membership_GetUserByUserId_Result)">
            <Parameter Name="UserId" Mode="In" Type="Guid" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="UpdateLastActivity" Mode="In" Type="Boolean" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_ResetPassword">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="NewPassword" Mode="In" Type="String" />
            <Parameter Name="MaxInvalidPasswordAttempts" Mode="In" Type="Int32" />
            <Parameter Name="PasswordAttemptWindow" Mode="In" Type="Int32" />
            <Parameter Name="PasswordSalt" Mode="In" Type="String" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="PasswordFormat" Mode="In" Type="Int32" />
            <Parameter Name="PasswordAnswer" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_SetPassword">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="NewPassword" Mode="In" Type="String" />
            <Parameter Name="PasswordSalt" Mode="In" Type="String" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
            <Parameter Name="PasswordFormat" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_UnlockUser">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Membership_UpdateUser">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="Email" Mode="In" Type="String" />
            <Parameter Name="Comment" Mode="In" Type="String" />
            <Parameter Name="IsApproved" Mode="In" Type="Boolean" />
            <Parameter Name="LastLoginDate" Mode="In" Type="DateTime" />
            <Parameter Name="LastActivityDate" Mode="In" Type="DateTime" />
            <Parameter Name="UniqueEmail" Mode="In" Type="Int32" />
            <Parameter Name="CurrentTimeUtc" Mode="In" Type="DateTime" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Users_CreateUser">
            <Parameter Name="ApplicationId" Mode="In" Type="Guid" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="IsUserAnonymous" Mode="In" Type="Boolean" />
            <Parameter Name="LastActivityDate" Mode="In" Type="DateTime" />
            <Parameter Name="UserId" Mode="InOut" Type="Guid" />
          </FunctionImport>
          <FunctionImport Name="aspnet_Users_DeleteUser">
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
            <Parameter Name="UserName" Mode="In" Type="String" />
            <Parameter Name="TablesToDeleteFrom" Mode="In" Type="Int32" />
            <Parameter Name="NumTablesDeletedFrom" Mode="InOut" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="CheckMemberApplication" ReturnType="Collection(Int32)">
            <Parameter Name="MemberID" Mode="In" Type="Int32" />
            <Parameter Name="ApplicationName" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="GetAffiliateMemberSources_ByAffiliateIDMemberID" ReturnType="Collection(AspNetMembershipModel.GetAffiliateMemberSources_ByAffiliateIDMemberID_Result)">
            <Parameter Name="affiliateID" Mode="In" Type="Int32" />
            <Parameter Name="memberID" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="GetAffiliateMemberSources_ByAffiliateIDValue" ReturnType="Collection(AspNetMembershipModel.GetAffiliateMemberSources_ByAffiliateIDValue_Result)">
            <Parameter Name="affiliateID" Mode="In" Type="Int32" />
            <Parameter Name="Value" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="GetAffiliateMemberSources_ByMemberID" ReturnType="Collection(AspNetMembershipModel.GetAffiliateMemberSources_ByMemberID_Result)">
          <Parameter Name="memberID" Mode="In" Type="Int32" />
          </FunctionImport>
        </EntityContainer>
        <EntityType Name="Member">
          <Key>
            <PropertyRef Name="memberID" />
          </Key>
          <Property Name="memberID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserID" Type="Guid" Nullable="false" />
          <Property Name="membertypeID" Type="Int32" Nullable="false" />
          <Property Name="titleID" Type="Int32" Nullable="false" />
          <Property Name="genderID" Type="Int32" Nullable="false" />
          <Property Name="affiliateID" Type="Int32" Nullable="false" />
          <Property Name="marketingID" Type="Int32" Nullable="false" />
          <Property Name="quidcoID" Type="Int32" Nullable="false" />
          <Property Name="memberReferralMemberID" Type="Int32" Nullable="false" />
          <Property Name="memberReferralCode" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberSourceID" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberForename" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberSurname" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberAddress1" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberAddress2" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberAddress3" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberAddress4" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberAddress5" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberPostcode" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="memberDob" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="memberUnsubscribeDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="memberUnsubscribe" Type="Boolean" Nullable="false" />
          <Property Name="memberUnsubscribeReason" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="memberPremium" Type="Boolean" Nullable="false" />
          <Property Name="memberActivated" Type="Boolean" Nullable="false" />
          <Property Name="memberEmailVerification" Type="Guid" Nullable="false" />
          <Property Name="memberTermsVersion" Type="Decimal" Precision="19" Scale="4" />
        </EntityType>
        <EntityType Name="Affiliate">
          <Key>
            <PropertyRef Name="affiliateID" />
          </Key>
          <Property Name="affiliateID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="clubID" Type="Int32" />
          <Property Name="applicationName" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="affiliateUsername" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="affiliatePassword" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="affiliateDescription" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="affiliateDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="affiliateTest" Type="Boolean" Nullable="false" />
          <Property Name="affiliateSuppress" Type="Boolean" Nullable="false" />
          <Property Name="affiliateDisplayInAdminSystem" Type="Boolean" Nullable="false" />
          <Property Name="affiliatePointsPerPoundEarningRate" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="affiliatePointsPerPoundRedeemRate" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="affiliateTransactionPointsCap" Type="Int32" />
          <Property Name="affiliateMemberPointsCap" Type="Int32" />
          <Property Name="affiliateMarketingOptIn" Type="Boolean" />
          <Property Name="affiliateDisableMemberRegistration" Type="Boolean" Nullable="false" />
          <Property Name="affiliatePointsStatusApplication" Type="Boolean" Nullable="false" />
        </EntityType>
        <ComplexType Name="aspnet_Membership_GetPassword_Result">
          <Property Type="String" Name="Column1" Nullable="true" MaxLength="128" />
          <Property Type="Int32" Name="Column2" Nullable="true" />
        </ComplexType>
        <ComplexType Name="aspnet_Membership_GetPasswordWithFormat_Result">
          <Property Type="String" Name="Column1" Nullable="true" MaxLength="128" />
          <Property Type="Int32" Name="Column2" Nullable="true" />
          <Property Type="String" Name="Column3" Nullable="true" MaxLength="128" />
          <Property Type="Int32" Name="Column4" Nullable="true" />
          <Property Type="Int32" Name="Column5" Nullable="true" />
          <Property Type="Boolean" Name="Column6" Nullable="true" />
          <Property Type="DateTime" Name="Column7" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="Column8" Nullable="true" Precision="23" />
        </ComplexType>
        <ComplexType Name="aspnet_Membership_GetUserByName_Result">
          <Property Type="String" Name="Email" Nullable="true" MaxLength="256" />
          <Property Type="String" Name="PasswordQuestion" Nullable="true" MaxLength="256" />
          <Property Type="String" Name="Comment" Nullable="true" MaxLength="Max" />
          <Property Type="Boolean" Name="IsApproved" Nullable="false" />
          <Property Type="DateTime" Name="CreateDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastLoginDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastActivityDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastPasswordChangedDate" Nullable="false" Precision="23" />
          <Property Type="Guid" Name="UserId" Nullable="false" />
          <Property Type="Boolean" Name="IsLockedOut" Nullable="false" />
          <Property Type="DateTime" Name="LastLockoutDate" Nullable="false" Precision="23" />
        </ComplexType>
        <ComplexType Name="aspnet_Membership_GetUserByUserId_Result">
          <Property Type="String" Name="Email" Nullable="true" MaxLength="256" />
          <Property Type="String" Name="PasswordQuestion" Nullable="true" MaxLength="256" />
          <Property Type="String" Name="Comment" Nullable="true" MaxLength="Max" />
          <Property Type="Boolean" Name="IsApproved" Nullable="false" />
          <Property Type="DateTime" Name="CreateDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastLoginDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastActivityDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="LastPasswordChangedDate" Nullable="false" Precision="23" />
          <Property Type="String" Name="UserName" Nullable="false" MaxLength="256" />
          <Property Type="Boolean" Name="IsLockedOut" Nullable="false" />
          <Property Type="DateTime" Name="LastLockoutDate" Nullable="false" Precision="23" />
        </ComplexType>
        <ComplexType Name="GetAffiliateMemberSources_ByAffiliateIDMemberID_Result">
          <Property Type="Int32" Name="sourceID" Nullable="false" />
          <Property Type="Int32" Name="affiliateID" Nullable="false" />
          <Property Type="Int32" Name="memberID" Nullable="false" />
          <Property Type="String" Name="sourceValue" Nullable="false" MaxLength="255" />
          <Property Type="DateTime" Name="sourceDate" Nullable="false" Precision="23" />
        </ComplexType>
        <ComplexType Name="GetAffiliateMemberSources_ByAffiliateIDValue_Result">
          <Property Type="Int32" Name="sourceID" Nullable="false" />
          <Property Type="Int32" Name="affiliateID" Nullable="false" />
          <Property Type="Int32" Name="memberID" Nullable="false" />
          <Property Type="String" Name="sourceValue" Nullable="false" MaxLength="255" />
          <Property Type="DateTime" Name="sourceDate" Nullable="false" Precision="23" />
        </ComplexType>
        <ComplexType Name="GetAffiliateMemberSources_ByMemberID_Result">
          <Property Type="Int32" Name="sourceID" Nullable="false" />
          <Property Type="Int32" Name="affiliateID" Nullable="false" />
          <Property Type="Int32" Name="memberID" Nullable="false" />
          <Property Type="String" Name="sourceValue" Nullable="false" MaxLength="255" />
          <Property Type="DateTime" Name="sourceDate" Nullable="false" Precision="23" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="AspNetMembershipModelStoreContainer" CdmEntityContainer="MembershipEntities1">
          <EntitySetMapping Name="aspnet_Applications">
            <EntityTypeMapping TypeName="AspNetMembershipModel.aspnet_Applications">
              <MappingFragment StoreEntitySet="aspnet_Applications">
                <ScalarProperty Name="ApplicationName" ColumnName="ApplicationName" />
                <ScalarProperty Name="LoweredApplicationName" ColumnName="LoweredApplicationName" />
                <ScalarProperty Name="ApplicationId" ColumnName="ApplicationId" />
                <ScalarProperty Name="Description" ColumnName="Description" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="aspnet_Membership">
            <EntityTypeMapping TypeName="AspNetMembershipModel.aspnet_Membership">
              <MappingFragment StoreEntitySet="aspnet_Membership">
                <ScalarProperty Name="ApplicationId" ColumnName="ApplicationId" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="PasswordFormat" ColumnName="PasswordFormat" />
                <ScalarProperty Name="PasswordSalt" ColumnName="PasswordSalt" />
                <ScalarProperty Name="MobilePIN" ColumnName="MobilePIN" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="LoweredEmail" ColumnName="LoweredEmail" />
                <ScalarProperty Name="PasswordQuestion" ColumnName="PasswordQuestion" />
                <ScalarProperty Name="PasswordAnswer" ColumnName="PasswordAnswer" />
                <ScalarProperty Name="IsApproved" ColumnName="IsApproved" />
                <ScalarProperty Name="IsLockedOut" ColumnName="IsLockedOut" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="LastLoginDate" ColumnName="LastLoginDate" />
                <ScalarProperty Name="LastPasswordChangedDate" ColumnName="LastPasswordChangedDate" />
                <ScalarProperty Name="LastLockoutDate" ColumnName="LastLockoutDate" />
                <ScalarProperty Name="FailedPasswordAttemptCount" ColumnName="FailedPasswordAttemptCount" />
                <ScalarProperty Name="FailedPasswordAttemptWindowStart" ColumnName="FailedPasswordAttemptWindowStart" />
                <ScalarProperty Name="FailedPasswordAnswerAttemptCount" ColumnName="FailedPasswordAnswerAttemptCount" />
                <ScalarProperty Name="FailedPasswordAnswerAttemptWindowStart" ColumnName="FailedPasswordAnswerAttemptWindowStart" />
                <ScalarProperty Name="Comment" ColumnName="Comment" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="aspnet_SchemaVersions">
            <EntityTypeMapping TypeName="AspNetMembershipModel.aspnet_SchemaVersions">
              <MappingFragment StoreEntitySet="aspnet_SchemaVersions">
                <ScalarProperty Name="Feature" ColumnName="Feature" />
                <ScalarProperty Name="CompatibleSchemaVersion" ColumnName="CompatibleSchemaVersion" />
                <ScalarProperty Name="IsCurrentVersion" ColumnName="IsCurrentVersion" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="aspnet_Usernames">
            <EntityTypeMapping TypeName="AspNetMembershipModel.aspnet_Usernames">
              <MappingFragment StoreEntitySet="aspnet_Usernames">
                <ScalarProperty Name="usernameID" ColumnName="usernameID" />
                <ScalarProperty Name="ApplicationName" ColumnName="ApplicationName" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="UserID" ColumnName="UserID" />
                <ScalarProperty Name="usernameLogin" ColumnName="usernameLogin" />
                <ScalarProperty Name="usernameEmail" ColumnName="usernameEmail" />
                <ScalarProperty Name="usernameEmail512" ColumnName="usernameEmail512" />
                <ScalarProperty Name="usernameEmail1" ColumnName="usernameEmail1" />
                <ScalarProperty Name="usernameEmailUTF32" ColumnName="usernameEmailUTF32" />
                <ScalarProperty Name="usernameMobile" ColumnName="usernameMobile" />
                <ScalarProperty Name="usernameDate" ColumnName="usernameDate" />
                <ScalarProperty Name="usernameSuppress" ColumnName="usernameSuppress" />
                <ScalarProperty Name="usernameHashChecked" ColumnName="usernameHashChecked" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="aspnet_Users">
            <EntityTypeMapping TypeName="AspNetMembershipModel.aspnet_Users">
              <MappingFragment StoreEntitySet="aspnet_Users">
                <ScalarProperty Name="ApplicationId" ColumnName="ApplicationId" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="LoweredUserName" ColumnName="LoweredUserName" />
                <ScalarProperty Name="MobileAlias" ColumnName="MobileAlias" />
                <ScalarProperty Name="IsAnonymous" ColumnName="IsAnonymous" />
                <ScalarProperty Name="LastActivityDate" ColumnName="LastActivityDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Members">
            <EntityTypeMapping TypeName="AspNetMembershipModel.Member">
              <MappingFragment StoreEntitySet="Members">
                <ScalarProperty Name="memberTermsVersion" ColumnName="memberTermsVersion" />
                <ScalarProperty Name="memberEmailVerification" ColumnName="memberEmailVerification" />
                <ScalarProperty Name="memberActivated" ColumnName="memberActivated" />
                <ScalarProperty Name="memberPremium" ColumnName="memberPremium" />
                <ScalarProperty Name="memberUnsubscribeReason" ColumnName="memberUnsubscribeReason" />
                <ScalarProperty Name="memberUnsubscribe" ColumnName="memberUnsubscribe" />
                <ScalarProperty Name="memberUnsubscribeDate" ColumnName="memberUnsubscribeDate" />
                <ScalarProperty Name="memberDob" ColumnName="memberDob" />
                <ScalarProperty Name="memberPostcode" ColumnName="memberPostcode" />
                <ScalarProperty Name="memberAddress5" ColumnName="memberAddress5" />
                <ScalarProperty Name="memberAddress4" ColumnName="memberAddress4" />
                <ScalarProperty Name="memberAddress3" ColumnName="memberAddress3" />
                <ScalarProperty Name="memberAddress2" ColumnName="memberAddress2" />
                <ScalarProperty Name="memberAddress1" ColumnName="memberAddress1" />
                <ScalarProperty Name="memberSurname" ColumnName="memberSurname" />
                <ScalarProperty Name="memberForename" ColumnName="memberForename" />
                <ScalarProperty Name="memberSourceID" ColumnName="memberSourceID" />
                <ScalarProperty Name="memberReferralCode" ColumnName="memberReferralCode" />
                <ScalarProperty Name="memberReferralMemberID" ColumnName="memberReferralMemberID" />
                <ScalarProperty Name="quidcoID" ColumnName="quidcoID" />
                <ScalarProperty Name="marketingID" ColumnName="marketingID" />
                <ScalarProperty Name="affiliateID" ColumnName="affiliateID" />
                <ScalarProperty Name="genderID" ColumnName="genderID" />
                <ScalarProperty Name="titleID" ColumnName="titleID" />
                <ScalarProperty Name="membertypeID" ColumnName="membertypeID" />
                <ScalarProperty Name="UserID" ColumnName="UserID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Affiliates">
            <EntityTypeMapping TypeName="AspNetMembershipModel.Affiliate">
              <MappingFragment StoreEntitySet="Affiliates">
                <ScalarProperty Name="affiliatePointsStatusApplication" ColumnName="affiliatePointsStatusApplication" />
                <ScalarProperty Name="affiliateDisableMemberRegistration" ColumnName="affiliateDisableMemberRegistration" />
                <ScalarProperty Name="affiliateMarketingOptIn" ColumnName="affiliateMarketingOptIn" />
                <ScalarProperty Name="affiliateMemberPointsCap" ColumnName="affiliateMemberPointsCap" />
                <ScalarProperty Name="affiliateTransactionPointsCap" ColumnName="affiliateTransactionPointsCap" />
                <ScalarProperty Name="affiliatePointsPerPoundRedeemRate" ColumnName="affiliatePointsPerPoundRedeemRate" />
                <ScalarProperty Name="affiliatePointsPerPoundEarningRate" ColumnName="affiliatePointsPerPoundEarningRate" />
                <ScalarProperty Name="affiliateDisplayInAdminSystem" ColumnName="affiliateDisplayInAdminSystem" />
                <ScalarProperty Name="affiliateSuppress" ColumnName="affiliateSuppress" />
                <ScalarProperty Name="affiliateTest" ColumnName="affiliateTest" />
                <ScalarProperty Name="affiliateDate" ColumnName="affiliateDate" />
                <ScalarProperty Name="affiliateDescription" ColumnName="affiliateDescription" />
                <ScalarProperty Name="affiliatePassword" ColumnName="affiliatePassword" />
                <ScalarProperty Name="affiliateUsername" ColumnName="affiliateUsername" />
                <ScalarProperty Name="applicationName" ColumnName="applicationName" />
                <ScalarProperty Name="clubID" ColumnName="clubID" />
                <ScalarProperty Name="affiliateID" ColumnName="affiliateID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="aspnet_Membership_CreateUser" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_CreateUser" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_FindUsersByEmail" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_FindUsersByEmail" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_FindUsersByName" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_FindUsersByName" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetAllUsers" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetAllUsers" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetNumberOfUsersOnline" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetNumberOfUsersOnline" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetPassword" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetPassword">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.aspnet_Membership_GetPassword_Result">
                <ScalarProperty Name="Column1" ColumnName="Column1" />
                <ScalarProperty Name="Column2" ColumnName="Column2" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetPasswordWithFormat" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetPasswordWithFormat">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.aspnet_Membership_GetPasswordWithFormat_Result">
                <ScalarProperty Name="Column1" ColumnName="Column1" />
                <ScalarProperty Name="Column2" ColumnName="Column2" />
                <ScalarProperty Name="Column3" ColumnName="Column3" />
                <ScalarProperty Name="Column4" ColumnName="Column4" />
                <ScalarProperty Name="Column5" ColumnName="Column5" />
                <ScalarProperty Name="Column6" ColumnName="Column6" />
                <ScalarProperty Name="Column7" ColumnName="Column7" />
                <ScalarProperty Name="Column8" ColumnName="Column8" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetUserByEmail" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetUserByEmail" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetUserByName" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetUserByName">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.aspnet_Membership_GetUserByName_Result">
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="PasswordQuestion" ColumnName="PasswordQuestion" />
                <ScalarProperty Name="Comment" ColumnName="Comment" />
                <ScalarProperty Name="IsApproved" ColumnName="IsApproved" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="LastLoginDate" ColumnName="LastLoginDate" />
                <ScalarProperty Name="LastActivityDate" ColumnName="LastActivityDate" />
                <ScalarProperty Name="LastPasswordChangedDate" ColumnName="LastPasswordChangedDate" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="IsLockedOut" ColumnName="IsLockedOut" />
                <ScalarProperty Name="LastLockoutDate" ColumnName="LastLockoutDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="aspnet_Membership_GetUserByUserId" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_GetUserByUserId">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.aspnet_Membership_GetUserByUserId_Result">
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="PasswordQuestion" ColumnName="PasswordQuestion" />
                <ScalarProperty Name="Comment" ColumnName="Comment" />
                <ScalarProperty Name="IsApproved" ColumnName="IsApproved" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="LastLoginDate" ColumnName="LastLoginDate" />
                <ScalarProperty Name="LastActivityDate" ColumnName="LastActivityDate" />
                <ScalarProperty Name="LastPasswordChangedDate" ColumnName="LastPasswordChangedDate" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="IsLockedOut" ColumnName="IsLockedOut" />
                <ScalarProperty Name="LastLockoutDate" ColumnName="LastLockoutDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="aspnet_Membership_ResetPassword" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_ResetPassword" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_SetPassword" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_SetPassword" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_UnlockUser" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_UnlockUser" />
          <FunctionImportMapping FunctionImportName="aspnet_Membership_UpdateUser" FunctionName="AspNetMembershipModel.Store.aspnet_Membership_UpdateUser" />
          <FunctionImportMapping FunctionImportName="aspnet_Users_CreateUser" FunctionName="AspNetMembershipModel.Store.aspnet_Users_CreateUser" />
          <FunctionImportMapping FunctionImportName="aspnet_Users_DeleteUser" FunctionName="AspNetMembershipModel.Store.aspnet_Users_DeleteUser" />
          <FunctionImportMapping FunctionImportName="CheckMemberApplication" FunctionName="AspNetMembershipModel.Store.CheckMemberApplication" />
          <FunctionImportMapping FunctionImportName="GetAffiliateMemberSources_ByAffiliateIDMemberID" FunctionName="AspNetMembershipModel.Store.GetAffiliateMemberSources_ByAffiliateIDMemberID">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.GetAffiliateMemberSources_ByAffiliateIDMemberID_Result">
                <ScalarProperty Name="sourceID" ColumnName="sourceID" />
                <ScalarProperty Name="affiliateID" ColumnName="affiliateID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="sourceValue" ColumnName="sourceValue" />
                <ScalarProperty Name="sourceDate" ColumnName="sourceDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="GetAffiliateMemberSources_ByAffiliateIDValue" FunctionName="AspNetMembershipModel.Store.GetAffiliateMemberSources_ByAffiliateIDValue">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.GetAffiliateMemberSources_ByAffiliateIDValue_Result">
                <ScalarProperty Name="sourceID" ColumnName="sourceID" />
                <ScalarProperty Name="affiliateID" ColumnName="affiliateID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="sourceValue" ColumnName="sourceValue" />
                <ScalarProperty Name="sourceDate" ColumnName="sourceDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="GetAffiliateMemberSources_ByMemberID" FunctionName="AspNetMembershipModel.Store.GetAffiliateMemberSources_ByMemberID">
            <ResultMapping>
              <ComplexTypeMapping TypeName="AspNetMembershipModel.GetAffiliateMemberSources_ByMemberID_Result">
                <ScalarProperty Name="sourceID" ColumnName="sourceID" />
                <ScalarProperty Name="affiliateID" ColumnName="affiliateID" />
                <ScalarProperty Name="memberID" ColumnName="memberID" />
                <ScalarProperty Name="sourceValue" ColumnName="sourceValue" />
                <ScalarProperty Name="sourceDate" ColumnName="sourceDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>